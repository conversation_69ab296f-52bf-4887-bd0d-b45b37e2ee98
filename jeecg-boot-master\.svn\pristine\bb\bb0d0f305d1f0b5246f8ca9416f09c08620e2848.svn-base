package org.jeecg.modules.crmfy.crmcustomer.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 客户信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-20
 * @Version: V1.0
 */
@Data
@TableName("crm_customer")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_customer对象", description="客户信息管理")
public class CrmCustomer implements Serializable{
    
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**客户唯一标识*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "客户唯一标识")
	private java.lang.Integer id;
	/**客户姓名*/
	@Excel(name = "客户姓名", width = 15)
    @ApiModelProperty(value = "客户姓名")
	private java.lang.String customerName;
	/**性别 0-未知 1-男 2-女*/
	@Excel(name = "性别 0-未知 1-男 2-女", width = 15)
    @ApiModelProperty(value = "性别 0-未知 1-男 2-女")
	private java.lang.Integer gender;
	/**出生日期*/
	@Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
	private java.util.Date birthdate;
	/**证件号*/
	@Excel(name = "证件号", width = 15)
    @ApiModelProperty(value = "证件号")
	private java.lang.String idNumber;
	/**证件类型 1身份证，2护照，3其他*/
	@Excel(name = "证件类型 1身份证，2护照，3其他", width = 15)
    @ApiModelProperty(value = "证件类型 1身份证，2护照，3其他")
	private java.lang.String idType;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
	private java.lang.String mobile;
	/**电子邮箱*/
	@Excel(name = "电子邮箱", width = 15)
    @ApiModelProperty(value = "电子邮箱")
	private java.lang.String email;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
	private java.lang.String address;
	/**职业信息*/
	@Excel(name = "职业信息", width = 15)
    @ApiModelProperty(value = "职业信息")
	private java.lang.String occupation;
	/**年收入*/
	@Excel(name = "年收入", width = 15)
    @ApiModelProperty(value = "年收入")
	private java.math.BigDecimal annualIncome;
	/**客户来源*/
	@Excel(name = "客户来源", width = 15)
    @ApiModelProperty(value = "客户来源")
	private java.lang.String customerSource;
	/**1-潜在客户 2-意向客户 3-已投保 4-失效客户*/
	@Excel(name = "1-潜在客户 2-意向客户 3-已投保 4-失效客户", width = 15)
    @ApiModelProperty(value = "1-潜在客户 2-意向客户 3-已投保 4-失效客户")
	private java.lang.String customerStatus;
	/**风险承受评估结果*/
	@Excel(name = "风险承受评估结果", width = 15)
    @ApiModelProperty(value = "风险承受评估结果")
	private java.lang.String riskAssessment;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
	
    @Dict(dictTable = "crm_customer_grade_config",dicText = "level_name",dicCode = "id")
	@Excel(name = "客户等级id", width = 15)
    @ApiModelProperty(value = "客户等级id")
	private Integer cusLevel;
	
	@Excel(name = "客户消费累计", width = 15)
    @ApiModelProperty(value = "客户消费累计")
	private java.lang.String cusFee;
}
