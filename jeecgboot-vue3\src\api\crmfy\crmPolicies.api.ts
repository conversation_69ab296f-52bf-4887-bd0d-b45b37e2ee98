import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';
import { CrmPolicyDTO } from '/@/types/crmfy/crmPolicies';

const { createConfirm } = useMessage();

enum Api {
  list = '/crmpolicies/crmPolicies/list',
  add = '/crmpolicies/crmPolicies/add',
  edit = '/crmpolicies/crmPolicies/edit',
  detail = '/crmpolicies/crmPolicies/detail',
  delete = '/crmpolicies/crmPolicies/delete',
  deleteBatch = '/crmpolicies/crmPolicies/deleteBatch',
  operationAudit = '/crmpolicies/crmPolicies/operationAudit',
  underwritingReply = '/crmpolicies/crmPolicies/underwritingReply',
  noticeReply = '/crmpolicies/crmPolicies/noticeReply',
  feesArrival = '/crmpolicies/crmPolicies/feesArrival',
  policyIssuance = '/crmpolicies/crmPolicies/policyIssuance',
  policyComplete = '/crmpolicies/crmPolicies/policyComplete',
  policyEdit = '/crmpolicies/crmPolicies/policyEdit',
  exportXls = '/crmpolicies/crmPolicies/exportXls',
  importExcel = '/crmpolicies/crmPolicies/importExcel',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 获取保单详情
 * @param id
 */
export const getPolicyDetail = (id) => defHttp.get({ url: Api.detail, params: { id } });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.get({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.get({ url: Api.deleteBatch, params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新（销售员）
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.add;
  return defHttp.post({ url, params });
};

/**
 * 运营岗审核提交
 * @param params
 */
export const operationAudit = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.operationAudit, params });
};

/**
 * 运营岗核保回复
 * @param params
 */
export const underwritingReply = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.underwritingReply, params });
};

/**
 * 运营岗照会回复
 * @param params
 */
export const noticeReply = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.noticeReply, params });
};

/**
 * 运营岗保费到账
 * @param params
 */
export const feesArrival = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.feesArrival, params });
};

/**
 * 运营岗保单签发
 * @param params
 */
export const policyIssuance = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.policyIssuance, params });
};

/**
 * 运营岗订单完成
 * @param params
 */
export const policyComplete = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.policyComplete, params });
};

/**
 * 运营岗订单完成后编辑
 * @param params
 */
export const policyEdit = (params: CrmPolicyDTO) => {
  return defHttp.post({ url: Api.policyEdit, params });
};
