package org.jeecg.modules.crmfy.crmcustomer.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CrmCustomerPO implements Serializable{/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	
	@ApiModelProperty(value = "客户唯一标识")
	private java.lang.Integer id;

	@ApiModelProperty(value = "客户姓名")
	private java.lang.String customerName;
	
	
	@ApiModelProperty(value = "部门")
	private java.lang.String orgCode;
	
	
	@ApiModelProperty(value = "客户经理")
	private java.lang.String username;

}
