package org.jeecg.modules.crmfy.crmcustomer.vo;

import java.io.Serializable;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CrmCustomerVO  implements Serializable {/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(value = "客户唯一标识")
	private java.lang.Integer id;
	/**客户姓名*/
	@Excel(name = "客户姓名", width = 15)
    @ApiModelProperty(value = "客户姓名")
	private java.lang.String customerName;
	/**性别 0-未知 1-男 2-女*/
	@Excel(name = "性别 0-未知 1-男 2-女", width = 15)
    @ApiModelProperty(value = "性别 0-未知 1-男 2-女")
	private java.lang.Integer gender;
	/**出生日期*/
	@Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
	private java.util.Date birthdate;
	/**证件号*/
	@Excel(name = "证件号", width = 15)
    @ApiModelProperty(value = "证件号")
	private java.lang.String idNumber;
	/**证件类型 1身份证，2护照，3其他*/
	@Excel(name = "证件类型 1身份证，2护照，3其他", width = 15)
    @ApiModelProperty(value = "证件类型 1身份证，2护照，3其他")
	private java.lang.String idType;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
	private java.lang.String mobile;
	/**电子邮箱*/
	@Excel(name = "电子邮箱", width = 15)
    @ApiModelProperty(value = "电子邮箱")
	private java.lang.String email;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
	private java.lang.String address;
	/**职业信息*/
//	@Excel(name = "职业信息", width = 15)
//    @ApiModelProperty(value = "职业信息")
//	private java.lang.String occupation;
	/**年收入*/
//	@Excel(name = "年收入", width = 15)
//    @ApiModelProperty(value = "年收入")
//	private java.math.BigDecimal annualIncome;
	/**客户来源*/
//	@Excel(name = "客户来源", width = 15)
//    @ApiModelProperty(value = "客户来源")
//	private java.lang.String customerSource;
	/**1-潜在客户 2-意向客户 3-已投保 4-失效客户*/
//	@Excel(name = "1-潜在客户 2-意向客户 3-已投保 4-失效客户", width = 15)
//    @ApiModelProperty(value = "1-潜在客户 2-意向客户 3-已投保 4-失效客户")
//	private java.lang.String customerStatus;
	/**风险承受评估结果*/
//	@Excel(name = "风险承受评估结果", width = 15)
//    @ApiModelProperty(value = "风险承受评估结果")
//	private java.lang.String riskAssessment;
	
	
//	@Excel(name = "客户经理", width = 15)
    @ApiModelProperty(value = "客户经理账号")
	private java.lang.String username;
    
    @ApiModelProperty(value = "客户经理")
	private java.lang.String realname;
	@Dict(dictTable = "crm_customer_grade_config",dicText = "level_name",dicCode = "id")
	@Excel(name = "客户等级", dictTable = "crm_customer_grade_config",dicText = "level_name",dicCode = "id",width = 15)
    @ApiModelProperty(value = "客户等级id")
	private java.lang.String cusLevel;
	
	
	
	
	
	

}
