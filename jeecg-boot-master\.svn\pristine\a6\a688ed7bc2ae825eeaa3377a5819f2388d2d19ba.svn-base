package org.jeecg.modules.crmfy.crmproducts.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 产品信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
@Data
@TableName("crm_products")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_products对象", description="产品信息管理")
public class CrmProducts implements Serializable{/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
    
	
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
	private java.lang.Integer id;
	
	
	/**产品唯一编码*/
	//@Excel(name = "产品唯一编码", width = 15)
    @ApiModelProperty(value = "产品唯一编码")
	private java.lang.String productCode;
	/**是否热门产品*/
	@Excel(name = "是否热门产品", width = 15)
    @ApiModelProperty(value = "是否热门产品")
	private java.lang.Boolean hot;
	/**产品类型*/
	@Excel(name = "产品类型", width = 15)
    @ApiModelProperty(value = "产品类型")
	private java.lang.String type;
	/**产品标签数组*/
	@Excel(name = "产品标签数组", width = 15)
    @ApiModelProperty(value = "产品标签数组")
	@TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray tags;
	
	
	@Excel(name = "产品英文名称", width = 15)
    @ApiModelProperty(value = "产品英文名称")
	private java.lang.String planName;
	
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @ApiModelProperty(value = "产品名称")
	private java.lang.String productName;
	
	
	/**合规产品名称*/
	@Excel(name = "合规产品名称", width = 15)
    @ApiModelProperty(value = "合规产品名称")
	private java.lang.String productComplianceName;
	/**分类编码*/
	@Excel(name = "分类编码", width = 15)
    @ApiModelProperty(value = "分类编码")
	private java.lang.String categoryCode;
	/**分类名称*/
	@Excel(name = "分类名称", width = 15)
    @ApiModelProperty(value = "分类名称")
	private java.lang.String categoryName;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
	private java.lang.String companyName;
	/**公司合规名称*/
	@Excel(name = "公司合规名称", width = 15)
    @ApiModelProperty(value = "公司合规名称")
	private java.lang.String companyComplianceName;
	/**公司代码*/
	@Excel(name = "公司代码", width = 15)
    @ApiModelProperty(value = "公司代码")
	private java.lang.String companyCode;
	/**产品描述*/
	@Excel(name = "产品描述", width = 15)
    @ApiModelProperty(value = "产品描述")
	@TableField("`desc`")
	private java.lang.String desc;
	/**Logo URL*/
	@Excel(name = "Logo URL", width = 15)
    @ApiModelProperty(value = "Logo URL")
	private java.lang.String logo;
	/**Banner URL*/
	@Excel(name = "Banner URL", width = 15)
    @ApiModelProperty(value = "Banner URL")
	private java.lang.String banner;
	/**是否可比价*/
	@Excel(name = "是否可比价", width = 15)
    @ApiModelProperty(value = "是否可比价")
	private java.lang.Integer comparable;
	/**是否可报价*/
	@Excel(name = "是否可报价", width = 15)
    @ApiModelProperty(value = "是否可报价")
	private java.lang.Integer quotable;
	/**支持币种数组*/
	@Excel(name = "支持币种数组", width = 15)
    @ApiModelProperty(value = "支持币种数组")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private JSONArray currencies;
	/**缴费频率数组*/
	@Excel(name = "缴费频率数组", width = 15)
    @ApiModelProperty(value = "缴费频率数组")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private JSONArray  frequencies;
	/**缴费年期数组*/
	@Excel(name = "缴费年期数组", width = 15)
    @ApiModelProperty(value = "缴费年期数组")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private JSONArray  paymentTerm;
	/**保费计算公式*/
	@Excel(name = "保费计算公式", width = 15)
    @ApiModelProperty(value = "保费计算公式")
	private java.lang.String quotationFormula;
	/**保费计算版本*/
	@Excel(name = "保费计算版本", width = 15)
    @ApiModelProperty(value = "保费计算版本")
	private java.lang.String quotationVersion;
	/**产品亮点数组*/
	@Excel(name = "产品亮点数组", width = 15)
    @ApiModelProperty(value = "产品亮点数组")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private JSONArray  highlights;
	/**建议书支持语言*/
	@Excel(name = "建议书支持语言", width = 15)
    @ApiModelProperty(value = "建议书支持语言")
	@TableField(typeHandler = FastjsonTypeHandler.class)
	private JSONArray  proposalLanguages;


	@Excel(name = "产品状态", width = 15)
    @ApiModelProperty(value = "产品状态 1-有效 0-下架")
	private java.lang.String productStatus;

	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人",hidden=true)
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间",hidden=true)
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人",hidden=true)
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间",hidden=true)
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除",hidden=true)
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID",hidden=true)
	private java.lang.Integer tenantId;
}
