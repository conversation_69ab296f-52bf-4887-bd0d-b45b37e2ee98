package org.jeecg.modules.crmfy.crmproducts.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.crmfy.crmproducts.entity.CrmProducts;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsQueryDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsVO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductAddDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductCreateDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 产品信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
public interface CrmProductsMapper extends BaseMapper<CrmProducts> {

	IPage<CrmProductsVO> queryProductInfo(Page<CrmProductsVO> page, CrmProductsQueryDTO crmProductsQueryDTO);

	ProductDetailVO queryDetailById(String id);

}
