package org.jeecg.modules.crmfy.crmpolicyadditional.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 保单附加险
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
@Data
@TableName("crm_policy_additional")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_policy_additional对象", description="保单附加险")
public class CrmPolicyAdditional {
    
	/**主键id*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
	private java.lang.Integer id;
	/**保单Id*/
	@Excel(name = "保单Id", width = 15)
    @ApiModelProperty(value = "保单Id")
	private java.lang.Integer policyId;
	/**对应版本号*/
	@Excel(name = "对应版本号", width = 15)
    @ApiModelProperty(value = "对应版本号")
	private java.lang.Integer version;
	/**保险产品id*/
	@Excel(name = "保险产品id", width = 15)
    @ApiModelProperty(value = "保险产品id")
	private java.lang.Integer productId;

	@ApiModelProperty(value = "保险产品名称")
	private java.lang.String productName;
	/**保险产品名称英文*/
	@Excel(name = "保险产品名称英文", width = 15)
    @ApiModelProperty(value = "保险产品名称英文")
	private java.lang.String planName;

	@Excel(name = "附加险每期保费", width = 15)
    @ApiModelProperty(value = "附加险每期保费")
	private BigDecimal additionalAmount;

	
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
}
