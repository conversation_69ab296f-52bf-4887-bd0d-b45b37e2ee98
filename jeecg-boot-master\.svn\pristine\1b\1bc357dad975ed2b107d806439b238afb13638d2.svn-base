package org.jeecg.modules.crmfy.crmpolicies.vo;

import java.io.Serializable;
import java.util.List;

import org.jeecg.common.desensitization.annotation.SensitiveField;
import org.jeecg.common.desensitization.enums.SensitiveEnum;
import org.jeecg.modules.crmfy.crmpolicyadditional.entity.CrmPolicyAdditional;
import org.jeecg.modules.crmfy.crmpolicybeneficiaries.entity.CrmPolicyBeneficiaries;
import org.jeecg.modules.crmfy.crmpolicyholders.entity.CrmPolicyHolders;
import org.jeecg.modules.crmfy.crmpolicyinsureds.entity.CrmPolicyInsureds;
import org.jeecgframework.poi.excel.annotation.Excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CrmPoliciesDTO implements Serializable {
	/**
	* 
	*/
	private static final long serialVersionUID = 1L;

	/** 保单ID */
	@ApiModelProperty(value = "保单ID", hidden = true)
	private java.lang.Integer id;
	/** 供应商名称 */
	@Excel(name = "供应商", width = 15)
	@ApiModelProperty(value = "供应商")
	private java.lang.String supplierNo;
	/** 保险产品名称 */
	// @Excel(name = "保险产品名称", width = 15)
	@ApiModelProperty(value = "保险产品名称")
	private java.lang.String productName;
	/** 保险产品编码 */
	// @Excel(name = "保险产品编码", width = 15)
	// @ApiModelProperty(value = "保险产品编码")
	// private java.lang.String productCode;

	@Excel(name = "保险产品名称(英文)", width = 15)
    @ApiModelProperty(value = "保险产品名称(英文)")
	private java.lang.String planName;
	
    @ApiModelProperty(value = "保险产品id")
	private java.lang.Integer productId;

	/** 缴费年期 */
	@Excel(name = "缴费年期", width = 15)
	@ApiModelProperty(value = "缴费年期")
	private java.lang.Integer paymentTerm;
	/** 缴费频率 */
	@Excel(name = "缴费频率", width = 15)
	@ApiModelProperty(value = "缴费频率")
	private java.lang.String paymentFrequency;
	/** 缴费币种 */
	@Excel(name = "缴费币种", width = 15)
	@ApiModelProperty(value = "缴费币种")
	private java.lang.String paymentCurrency;
	/** 缴费方式 */
	@Excel(name = "缴费方式", width = 15)
	@ApiModelProperty(value = "缴费方式")
	private java.lang.String paymentMethod;
	/** 缴费金额 */
	@Excel(name = "缴费金额", width = 15)
	@ApiModelProperty(value = "缴费金额")
	private java.math.BigDecimal paymentAmount;
	/** 保单号码 */
	@Excel(name = "保单号码", width = 15)
	@ApiModelProperty(value = "保单号码")
	private java.lang.String policyNo;
	/** 保障期限 */
	@Excel(name = "保障期限", width = 15)
	@ApiModelProperty(value = "保障期限")
	private java.lang.String coveragePeriod;
	/** 是否自动扣款 */
	@Excel(name = "是否自动扣款", width = 15)
	@ApiModelProperty(value = "是否自动扣款")
	private java.lang.Boolean autoDebit;
	/** 优惠方案 */
	@Excel(name = "优惠方案", width = 15)
	@ApiModelProperty(value = "优惠方案")
	private java.lang.String promotionPlan;
	/** 通知电邮地址 */
	@SensitiveField(type=SensitiveEnum.EMAIL)
	@Excel(name = "通知电邮地址", width = 15)
	@ApiModelProperty(value = "通知电邮地址")
	private java.lang.String notificationEmail;
	/** 电子签收链接 */
	@Excel(name = "电子签收链接", width = 15)
	@ApiModelProperty(value = "电子签收链接")
	private java.lang.String signatureSms;
	/** 当前数据版本 */
	@Excel(name = "当前数据版本", width = 15)
	@ApiModelProperty(value = "当前数据版本")
	private java.lang.Integer currentVersion;
	/** 保单状态 */
	@Excel(name = "保单状态", width = 15)
	@ApiModelProperty(value = "保单状态")
	private java.lang.String policyStatus;

	/** 缴费日当天汇率 */
	@Excel(name = "缴费日当天汇率", width = 15)
	@ApiModelProperty(value = "缴费日当天汇率")
	private java.math.BigDecimal fypExchangeRate;
	/** 佣金结算日当天汇率 */
	@Excel(name = "佣金结算日当天汇率", width = 15)
	@ApiModelProperty(value = "佣金结算日当天汇率")
	private java.math.BigDecimal rcExchangeRate;

	@ApiModelProperty(value = "投保人信息")
	private CrmPolicyHolders crmPolicyHolders;

	@ApiModelProperty(value = "被保人信息")
	private CrmPolicyInsureds crmPolicyInsureds;

	@ApiModelProperty(value = "受益人信息")
	private CrmPolicyBeneficiaries crmPolicyBeneficiaries;

	@ApiModelProperty(value = "保单附加险信息")
	private List<CrmPolicyAdditional> crmPolicyAdditionals;

	@ApiModelProperty(value = "客户ID")
	private java.lang.Integer customerId;

	@ApiModelProperty(value = "销售经理账号")
	private java.lang.String salesUsername;

	@ApiModelProperty(value = "是否提交 1是， 0否，(销售员保存或提交时传)")
	private String izSubmit;

	// @Excel(name = "核保结果", width = 15)
	@ApiModelProperty(value = "核保结果：0-核保不通过（照会） 1-核保通过，(运营提交核保回复时传)")
	private java.lang.String auditResult;

	@ApiModelProperty(value = "保单唯一标识")
	private java.lang.String policyUuid;

	@ApiModelProperty(value = "变更原因，(订单完成后运营修改时传)")
	private java.lang.String changeReason;

	/** 照会说明 */
	@Excel(name = "照会说明", width = 15)
	@ApiModelProperty(value = "照会说明 (核保不通过时传)")
	private java.lang.String noticeRemark;


	@ApiModelProperty(value = "照会回复-(销售员处理完照会时传)")
	private java.lang.String noticeReply;

	@ApiModelProperty(value = "下一个节点流程 状态：1-保单提交 2-运营审核 3-核保回复 4-照会回复 5-保费到账确认 6-保单签发")
	private java.lang.String nextNode;

}
