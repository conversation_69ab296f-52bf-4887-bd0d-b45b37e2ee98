package org.jeecg.modules.crmfy.crmproducts.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.SymbolConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.util.filter.SsrfFileTypeFilter;
import org.jeecg.modules.crmfy.crmproducts.entity.CrmProducts;
import org.jeecg.modules.crmfy.crmproducts.service.ICrmProductsService;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsQueryDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsVO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductAddDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductCreateDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailVO;
import org.jeecg.modules.jsjx.sysuserlog.entity.SysUserLogCus;

import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 产品信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
@Slf4j
@Api(tags="产品信息管理")
@RestController
@RequestMapping("/crmproducts/crmProducts")
public class CrmProductsController extends JeecgController<CrmProducts, ICrmProductsService> {
	@Autowired
	private ICrmProductsService crmProductsService;


    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    /**
     * 本地：local minio：minio 阿里：alioss
     */
    @Value(value="${jeecg.uploadType}")
    private String uploadType;

	/**
	 * 分页列表查询
	 *
	 * @param crmProducts
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "产品信息管理-分页列表查询")
	@ApiOperation(value="产品信息管理-分页列表查询", notes="产品信息管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CrmProductsVO> > queryPageList(CrmProductsQueryDTO crmProducts,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


		 LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();

	     if (StringUtils.isBlank( user.getOrgCode())) {
	         return Result.error("该账号未绑定部门，请重试");
	     }




		Page<CrmProductsVO> page = new Page<CrmProductsVO>(pageNo, pageSize);

		IPage<CrmProductsVO> pageList  = crmProductsService.queryProductInfo(page,crmProducts) ;

		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param crmProducts
	 * @return
	 */
	@AutoLog(value = "产品信息管理-添加")
	@ApiOperation(value="产品信息管理-添加", notes="产品信息管理-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ProductAddDTO crmProduct) {
		crmProductsService.saveProduct(crmProduct);
		return Result.OK("添加成功！");
	}


	@AutoLog(value = "产品资讯表-通过产品编码添加")
	// @ApiOperation(value="产品资讯表-通过产品编码添加", notes="产品资讯表-通过产品编码添加")
	@PostMapping(value = "/addByProductCode")
	public Result<?> addByProductCode(String productCodes) {
		crmProductsService.saveProducts(productCodes);
		return Result.OK("添加成功！");
	}


	@ApiOperation(value="产品信息管理-文件上传", notes="产品信息管理-文件上传")
    @PostMapping(value = "/upload")
    public Result<?> upload(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Result<?> result = new Result<>();
        String savePath = "";
        String bizPath = request.getParameter("biz");

        //LOWCOD-2580 sys/common/upload接口存在任意文件上传漏洞
        if (oConvertUtils.isNotEmpty(bizPath)) {
            if(bizPath.contains(SymbolConstant.SPOT_SINGLE_SLASH) || bizPath.contains(SymbolConstant.SPOT_DOUBLE_BACKSLASH)){
                throw new JeecgBootException("上传目录bizPath，格式非法！");
            }
        }

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        // 获取上传文件对象
        MultipartFile file = multipartRequest.getFile("file");
        if(oConvertUtils.isEmpty(bizPath)){
            if(CommonConstant.UPLOAD_TYPE_OSS.equals(uploadType)){
                //未指定目录，则用阿里云默认目录 upload
                bizPath = "upload";
                //result.setMessage("使用阿里云文件上传时，必须添加目录！");
                //result.setSuccess(false);
                //return result;
            }else{
                bizPath = "";
            }
        }
        if(CommonConstant.UPLOAD_TYPE_LOCAL.equals(uploadType)){
            //update-begin-author:liusq date:20221102 for: 过滤上传文件类型
            SsrfFileTypeFilter.checkUploadFileType(file);
            //update-end-author:liusq date:20221102 for: 过滤上传文件类型
            //update-begin-author:lvdandan date:20200928 for:修改JEditor编辑器本地上传
            savePath = this.uploadLocal(file,bizPath);
            //update-begin-author:lvdandan date:20200928 for:修改JEditor编辑器本地上传
            /**  富文本编辑器及markdown本地上传时，采用返回链接方式
            //针对jeditor编辑器如何使 lcaol模式，采用 base64格式存储
            String jeditor = request.getParameter("jeditor");
            if(oConvertUtils.isNotEmpty(jeditor)){
                result.setMessage(CommonConstant.UPLOAD_TYPE_LOCAL);
                result.setSuccess(true);
                return result;
            }else{
                savePath = this.uploadLocal(file,bizPath);
            }
            */
        }else{
            //update-begin-author:taoyan date:20200814 for:文件上传改造
            savePath = CommonUtils.upload(file, bizPath, uploadType);
            //update-end-author:taoyan date:20200814 for:文件上传改造
        }
        if(oConvertUtils.isNotEmpty(savePath)){
            result.setMessage(savePath);
            result.setSuccess(true);
        }else {
            result.setMessage("上传失败！");
            result.setSuccess(false);
        }
        return result;
    }


    /**
     * 本地文件上传
     * @param mf 文件
     * @param bizPath  自定义路径
     * @return
     */
    private String uploadLocal(MultipartFile mf,String bizPath){
        try {
            String ctxPath = uploadpath;
            String fileName = null;
            File file = new File(ctxPath + File.separator + bizPath + File.separator );
            if (!file.exists()) {
                // 创建文件根目录
                file.mkdirs();
            }
            // 获取文件名
            String orgName = mf.getOriginalFilename();
            orgName = CommonUtils.getFileName(orgName);
            if(orgName.indexOf(SymbolConstant.SPOT)!=-1){
                fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
            }else{
                fileName = orgName+ "_" + System.currentTimeMillis();
            }
            String savePath = file.getPath() + File.separator + fileName;
            File savefile = new File(savePath);
            FileCopyUtils.copy(mf.getBytes(), savefile);
            String dbpath = null;
            if(oConvertUtils.isNotEmpty(bizPath)){
                dbpath = bizPath + File.separator + fileName;
            }else{
                dbpath = fileName;
            }
            if (dbpath.contains(SymbolConstant.DOUBLE_BACKSLASH)) {
                dbpath = dbpath.replace(SymbolConstant.DOUBLE_BACKSLASH, SymbolConstant.SINGLE_SLASH);
            }
            return dbpath;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }





	/**
	 * 编辑
	 *
	 * @param crmProducts
	 * @return
	 */
	@AutoLog(value = "产品信息管理-编辑")
	@ApiOperation(value="产品信息管理-编辑", notes="产品信息管理-编辑")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<?> edit(@RequestBody ProductAddDTO crmProduct) {
		crmProductsService.updateCrmProductById(crmProduct);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "产品信息管理-通过id删除")
	@ApiOperation(value="产品信息管理-通过id删除", notes="产品信息管理-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		this.crmProductsService.removeProductByIds(Arrays.asList(id.split(",")));
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "产品信息管理-批量删除")
	@ApiOperation(value="产品信息管理-批量删除", notes="产品信息管理-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.crmProductsService.removeProductByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "产品信息管理-通过id查询")
	@ApiOperation(value="产品信息管理-通过id查询", notes="产品信息管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProductDetailVO> queryById(@RequestParam(name="id",required=true) String id) {
		ProductDetailVO crmProduct = crmProductsService.queryDetailById(id);
		return Result.OK(crmProduct);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param crmProducts
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, CrmProducts crmProducts) {
      return super.exportXls(request, crmProducts, CrmProducts.class, "产品信息管理");
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      return super.importExcel(request, response, CrmProducts.class);
  }

  /**
   * 根据供应商编码查询产品列表
   *
   * @param supplierCode 供应商编码
   * @param keyword 搜索关键词
   * @return
   */
  @AutoLog(value = "产品信息管理-根据供应商查询")
  @ApiOperation(value="产品信息管理-根据供应商查询", notes="产品信息管理-根据供应商查询")
  @GetMapping(value = "/searchProductsBySupplier")
  public Result<List<CrmProducts>> searchProductsBySupplier(
      @RequestParam(name="supplierCode", required=true) String supplierCode,
      @RequestParam(name="keyword", required=false) String keyword) {

      QueryWrapper<CrmProducts> queryWrapper = new QueryWrapper<>();
      queryWrapper.eq("company_code", supplierCode);
      queryWrapper.eq("product_status", "1"); // 只查询已上架的产品

      if(oConvertUtils.isNotEmpty(keyword)) {
          queryWrapper.and(wrapper -> wrapper
              .like("product_name", keyword)
              .or()
              .like("plan_name", keyword));
      }

      queryWrapper.orderByDesc("create_time");
      queryWrapper.last("limit 50"); // 限制返回50条记录
      List<CrmProducts> list = crmProductsService.list(queryWrapper);
      return Result.OK(list);
  }
}
