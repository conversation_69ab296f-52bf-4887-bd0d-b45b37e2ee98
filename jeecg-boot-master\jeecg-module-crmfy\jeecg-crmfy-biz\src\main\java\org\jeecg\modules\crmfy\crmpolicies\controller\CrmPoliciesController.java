package org.jeecg.modules.crmfy.crmpolicies.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crmfy.crmpolicies.entity.CrmPolicies;
import org.jeecg.modules.crmfy.crmpolicies.service.ICrmPoliciesService;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesDTO;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesPO;
import org.jeecg.modules.crmfy.crmpolicies.vo.CrmPoliciesVO;
import org.jeecg.modules.jsjx.enums.PostEnum;

import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 订单信息管理
 * @Author: jeecg-boot
 * @Date: 2025-03-28
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "订单信息管理")
@RestController
@RequestMapping("/crmpolicies/crmPolicies")
public class CrmPoliciesController extends JeecgController<CrmPolicies, ICrmPoliciesService> {
	@Autowired
	private ICrmPoliciesService crmPoliciesService;

	/**
	 * 分页列表查询
	 *
	 * @param crmPolicies
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "订单信息管理-分页列表查询")
	@ApiOperation(value = "订单信息管理-分页列表查询", notes = "订单信息管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CrmPoliciesVO>> queryPageList(CrmPoliciesPO crmPoliciesPO,
			@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			HttpServletRequest req) {
		// 获取当前登录用户
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if (StringUtils.isBlank(loginUser.getOrgCode())) {
			return Result.error("该账号未绑定部门，请重试");
		}
	
		// 根据用户部门类型设置查询条件
		if (!loginUser.getOrgCategory().equals("1")) {
			crmPoliciesPO.setOrgCode(loginUser.getOrgCode());
		}
		if (loginUser.getOrgCategory().equals("13")) {
			crmPoliciesPO.setSalesUsername(loginUser.getUsername());
		}

		if(StringUtils.isBlank(crmPoliciesPO.getIzActive())){
			crmPoliciesPO.setIzActive("1");
		}
	
		// 创建分页对象
		Page<CrmPoliciesVO> page = new Page<CrmPoliciesVO>(pageNo, pageSize);
		
		// 调用服务层查询方法
		IPage<CrmPoliciesVO> pageList =null;
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			return Result.error("操作失败，请先分配职务");
		}


		if (post.equals(PostEnum.XSKHJL.getCode())||post.equals(PostEnum.YYRY.getCode())) {// 销售客户经理或营业员不需要脱敏
			pageList = crmPoliciesService.queryPoliciesInfo(page, crmPoliciesPO);
		}else{
			pageList = crmPoliciesService.queryPoliciesInfoEncode(page, crmPoliciesPO);
		}
		
		
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param crmPolicies
	 * @return
	 */
	// @RequiresPermissions("crmpolicies:add")
	@AutoLog(value = "订单信息管理-保存/提交")
	@ApiOperation(value = "订单信息管理-保存/提交(销售经理-提交后销售经理不能修改)", notes = "订单信息管理-保存/提交")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {

		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			return Result.error("操作失败，请先分配职务");
		}
		if (!post.equals(PostEnum.XSKHJL.getCode())) {// 销售客户经理
			return Result.error("操作失败，仅销售客户经理可以操作！");
		}
		crmPoliciesService.saveCrmPolicies(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}


	@ApiOperation(value = "订单信息管理-保单详情查询", notes = "订单信息管理-保单详情查询")
	@GetMapping(value = "/detail")
	public Result<CrmPoliciesDTO> detail(@RequestParam(name = "id", required = true) String id) {
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String post = loginUser.getPostCode();
		if (StringUtils.isBlank(post)) {
			return Result.error("操作失败，请先分配职务");
		}

		CrmPoliciesDTO crmPoliciesDTO = new CrmPoliciesDTO();
		if (post.equals(PostEnum.XSKHJL.getCode())||post.equals(PostEnum.YYRY.getCode())) {// 销售客户经理或营业员不需要脱敏
			 crmPoliciesDTO = crmPoliciesService.queryCrmPoliciesById(id);
		}else{
			crmPoliciesDTO = crmPoliciesService.queryCrmPoliciesByIdEncode(id);
		}
		
		return Result.OK(crmPoliciesDTO);

	}

	/**
	 * 编辑
	 *
	 * @param crmPolicies
	 * @return
	 */
	@AutoLog(value = "订单信息管理-编辑/照会回复（销售员）")
	@ApiOperation(value = "订单信息管理-编辑/照会回复（销售员）", notes = "订单信息管理-编辑/照会回复（销售员）")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<?> edit(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {
		crmPoliciesService.updateCrmPoliciesByIdSales(crmPoliciesDTO);
		return Result.OK("操作成功!");
	}

	/**
	 * 运营岗审核修改
	 *
	 * @param crmPoliciesDTO
	 * @return
	 */
	@AutoLog(value = "订单信息管理-审核提交(运营岗)")
	@ApiOperation(value = "订单信息管理-审核提交(运营岗)", notes = "订单信息管理-审核提交(运营岗)")
	@PostMapping(value = "/operationAudit")
	public Result<?> operationAudit(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {
		// LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		// String post = loginUser.getPost();
		// if (StringUtils.isBlank(post)) {
		// 	return Result.error("操作失败，请先分配职务");
		// }
		// if (!post.equals(PostEnum.YYRY.getCode())) {// 运营岗
		// 	return Result.error("操作失败，仅运营岗可以操作！");
		// }
		crmPoliciesService.updateCrmPoliciesById(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}

	/**
	 * 运营岗核保回复
	 *
	 * @param crmPoliciesDTO
	 * @return
	 */
	@AutoLog(value = "订单信息管理-核保回复(运营岗)")
	@ApiOperation(value = "订单信息管理-核保回复(运营岗)", notes = "订单信息管理-核保回复(运营岗)")
	@PostMapping(value = "/underwritingReply")
	public Result<?> underwritingReply(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {
		// LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		// String post = loginUser.getPost();
		// if (StringUtils.isBlank(post)) {
		// 	return Result.error("操作失败，请先分配职务");
		// }
		// if (!post.equals(PostEnum.YYRY.getCode())) {// 运营岗
		// 	return Result.error("操作失败，仅运营岗可以操作！");
		// }
		crmPoliciesService.updateCrmPoliciesById(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}

		/**
	 * 运营岗核保回复
	 *
	 * @param crmPoliciesDTO
	 * @return
	 */
	@AutoLog(value = "订单信息管理-保费到账(运营岗)")
	@ApiOperation(value = "订单信息管理-保费到账(运营岗)", notes = "订单信息管理-保费到账(运营岗)")
	@PostMapping(value = "/feesArrival")
	public Result<?> feesArrival(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {

		crmPoliciesService.updateCrmPoliciesById(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}


	@AutoLog(value = "订单信息管理-保单签发(运营岗)")
	@ApiOperation(value = "订单信息管理-保单签发(运营岗)", notes = "订单信息管理-保单签发(运营岗)")
	@PostMapping(value = "/policyIssuance")
	public Result<?> policyIssuance(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {

		crmPoliciesService.updateCrmPoliciesById(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}


	@AutoLog(value = "订单信息管理-订单完成(运营岗)")
	@ApiOperation(value = "订单信息管理-订单完成(运营岗)", notes = "订单信息管理-订单完成(运营岗)")
	@PostMapping(value = "/policyComplete")
	public Result<?> policyComplete(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {

		crmPoliciesService.updateCrmPoliciesById(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}

	@AutoLog(value = "订单信息管理-订单完成后编辑(运营岗)")
	@ApiOperation(value = "订单信息管理-订单完成后编辑(运营岗)", notes = "订单信息管理-订单完成后编辑(运营岗)")
	@PostMapping(value = "/policyEdit")
	public Result<?> policyEdit(@RequestBody CrmPoliciesDTO crmPoliciesDTO) {

		crmPoliciesService.updateCrmPoliciesById(crmPoliciesDTO);
		return Result.OK("操作成功！");
	}


	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "订单信息管理-通过id删除")
	@ApiOperation(value = "订单信息管理-通过id删除", notes = "订单信息管理-通过id删除")
	@GetMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
		crmPoliciesService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	// @AutoLog(value = "订单信息管理-批量删除")
	// @ApiOperation(value = "订单信息管理-批量删除", notes = "订单信息管理-批量删除")
	// @GetMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		this.crmPoliciesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	// @AutoLog(value = "订单信息管理-通过id查询")
	// @ApiOperation(value = "订单信息管理-通过id查询", notes = "订单信息管理-通过id查询")
	// @GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
		CrmPolicies crmPolicies = crmPoliciesService.getById(id);
		return Result.OK(crmPolicies);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param crmPolicies
	 */
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, CrmPolicies crmPolicies) {
		return super.exportXls(request, crmPolicies, CrmPolicies.class, "订单信息管理");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, CrmPolicies.class);
	}

}
