<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmproducts.mapper.CrmProductsMapper">


<select id="queryProductInfo" parameterType="org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsQueryDTO" resultType="org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsVO">


  SELECT a.* from `crm_products` a 

  <where>
  
  	<if test="crmProductsQueryDTO.categoryCode !=null and crmProductsQueryDTO.categoryCode !=''">
			 and a.category_code= #{crmProductsQueryDTO.categoryCode} 
	</if>
	
	<if test="crmProductsQueryDTO.companyCode !=null and crmProductsQueryDTO.companyCode !=''">
			 and  a.company_code= #{crmProductsQueryDTO.companyCode} 
	</if>
	
	<if test="crmProductsQueryDTO.hot !=null and crmProductsQueryDTO.hot !=''">
			 and  a.hot= #{crmProductsQueryDTO.hot} 
	</if>
	
	<if test="crmProductsQueryDTO.keyWords !=null and crmProductsQueryDTO.keyWords !=''">
			 and  (     a.product_name like CONCAT('%',#{crmProductsQueryDTO.keyWords},'%')  
			 		or  a.category_name like  CONCAT('%',#{crmProductsQueryDTO.keyWords},'%') 
			 		or a.company_code like CONCAT('%',#{crmProductsQueryDTO.keyWords},'%') 
			 		or a.company_name like CONCAT('%',#{crmProductsQueryDTO.keyWords},'%') 
			      )
	</if>
	

  </where> 
  
  order by  a.company_code ,a.create_time desc



	</select>
	
	
  <resultMap id="productDetailVO1" type="org.jeecg.modules.crmfy.crmproducts.vo.ProductCreateDTO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="product_code" property="productCode" jdbcType="VARCHAR" />
    <result column="hot" property="hot" jdbcType="BIT" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="product_name" property="productName" jdbcType="VARCHAR" />
    <result column="product_compliance_name" property="productComplianceName" jdbcType="VARCHAR" />
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="company_compliance_name" property="companyComplianceName" jdbcType="VARCHAR" />
    <result column="company_code" property="companyCode" jdbcType="VARCHAR" />
    <result column="logo" property="logo" jdbcType="VARCHAR" />
    <result column="banner" property="banner" jdbcType="VARCHAR" />
    <result column="comparable" property="comparable" jdbcType="BIT" />
    <result column="quotable" property="quotable" jdbcType="BIT" />
    <result column="quotation_formula" property="quotationFormula" jdbcType="VARCHAR" />
    <result column="quotation_version" property="quotationVersion" jdbcType="VARCHAR" />
<!--     <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" /> -->
<!--     <result column="create_by" property="createBy" jdbcType="VARCHAR" /> -->
<!--     <result column="create_time" property="createTime" jdbcType="TIMESTAMP" /> -->
<!--     <result column="update_by" property="updateBy" jdbcType="VARCHAR" /> -->
<!--     <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" /> -->
<!--     <result column="del_flag" property="delFlag" jdbcType="INTEGER" /> -->
<!--     <result column="tenant_id" property="tenantId" jdbcType="INTEGER" /> -->
    
     <result column="tags" property="tags" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="desc" property="desc" jdbcType="VARCHAR"  />
    <result column="currencies" property="currencies" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    <result column="frequencies" property="frequencies" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    <result column="payment_term" property="paymentTerm" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    <result column="highlights" property="highlights" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    <result column="proposal_languages" property="proposalLanguages" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    
    
    <collection property="details" ofType="org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailDTO" javaType="list">
        <id column="detail_id" property="id" jdbcType="INTEGER" />
    	<result column="key" property="key" jdbcType="VARCHAR" />
    	<result column="name" property="name" jdbcType="VARCHAR" />
    	<result column="rank" property="rank" jdbcType="INTEGER" />
   		<result column="name_i18n" property="nameI18n" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
   		     <collection property="attributes" ofType="org.jeecg.modules.crmfy.crmdetailattributes.entity.CrmDetailAttributes" javaType="list">
   		     		<id column="attr_id" property="id" jdbcType="INTEGER" />
   		     	    <result column="attribute" property="attribute" jdbcType="VARCHAR" />
    				<result column="attr_name" property="name" jdbcType="VARCHAR" />
    				<result column="attr_rank" property="rank" jdbcType="INTEGER" />
    				<result column="attr_name_i18n" property="nameI18n" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    				<result column="value" property="value" jdbcType="LONGVARCHAR"  />
   				    <result column="value_i18n" property="valueI18n" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
   		 
    		</collection>
    </collection>
    
    <collection property="files" ofType="org.jeecg.modules.crmfy.crmproductfiles.entity.CrmProductFiles" javaType="list">  
    		<id column="file_id" property="id" jdbcType="INTEGER" />
    		<result column="file_name" property="fileName" jdbcType="VARCHAR" />
   			<result column="file_path" property="filePath" jdbcType="VARCHAR" />
   			<result column="file_type" property="fileType" jdbcType="VARCHAR" />

   			<result column="file_tags" property="tags" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    </collection>
    
    
  </resultMap>



    <resultMap id="productDetailVO" type="org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailVO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="product_code" property="productCode" jdbcType="VARCHAR" />
    <result column="hot" property="hot" jdbcType="BIT" />
    <result column="product_name" property="productName" jdbcType="VARCHAR" />
    <result column="plan_name" property="planName" jdbcType="VARCHAR" />
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
    <result column="company_code" property="companyCode" jdbcType="VARCHAR" />
  
    <result column="desc" property="desc" jdbcType="VARCHAR"  />
    <result column="currencies" property="currencies" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
    <result column="frequencies" property="frequencies" jdbcType="LONGVARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
        <result column="product_status" property="productStatus" jdbcType="VARCHAR"  />

    
    <collection property="paymentTerms" ofType="org.jeecg.modules.crmfy.crmproducts.vo.PaymentTermDTO" javaType="list">
        <id column="pay_term" property="paymentTerm" jdbcType="INTEGER" />

   		     <collection property="contractRates" ofType="org.jeecg.modules.crmfy.crmproductcontractrate.entity.CrmProductContractRate" javaType="list">
   		     		<id column="rate_id" property="id" jdbcType="INTEGER" />
    				<result column="year_seq" property="yearSeq" jdbcType="INTEGER" />
    				<result column="contract_rate" property="contractRate" jdbcType="DECIMAL"  />
   		 
    		</collection>
    </collection>
    
    <collection property="files" ofType="org.jeecg.modules.crmfy.crmproductfiles.entity.CrmProductFiles" javaType="list">  
    		<id column="file_id" property="id" jdbcType="INTEGER" />
    		<result column="file_name" property="fileName" jdbcType="VARCHAR" />
   			<result column="file_path" property="filePath" jdbcType="VARCHAR" />
   			<result column="file_type" property="fileType" jdbcType="VARCHAR" />

    </collection>
    
    
  </resultMap>
  
  
  <select id="getDetailById" resultMap="productDetailVO1">
  
  		SELECT a.*, 
				 b.`key`, b.`name`, b.`name_i18n`, b.`rank`,b.id as detail_id,
				 c.`attribute`, c.`name` as attr_name, c.`name_i18n` as attr_name_i18n, c.`value`,
				 c.`value_i18n`, c.`rank` as attr_rank ,c.id as attr_id,
				 d.`file_name`, d.`file_path`, d.`tags` as file_tags ,d.id as file_id
	from `crm_products` a 
	LEFT JOIN `crm_product_details` b on a.product_code =  b.product_code 
	LEFT JOIN crm_detail_attributes c on b.id = c.detail_id
	LEFT JOIN crm_product_files d on a.product_code = d.product_code
	<where>
	    and  a.id =#{id}
	</where>
  
  </select>


    <select id="queryDetailById" resultMap="productDetailVO">
  
  		SELECT a.*, 
				 b.payment_term as pay_term,
				 c.id as rate_id,c.year_seq,c.contract_rate,

				 d.`file_name`, d.`file_path`,d.file_type,d.id as file_id
	from `crm_products` a 
	LEFT JOIN crm_product_payment_terms b  on a.id =  b.product_id
	LEFT JOIN crm_product_contract_rate c on b.id = c.term_id
	LEFT JOIN crm_product_files d on a.id = d.product_id
	<where>
	    and  a.id =#{id}
	</where>
     order by  b.payment_term,c.year_seq
  </select>
	
	

</mapper>