package org.jeecg.modules.crmfy.crmcustomer.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crmfy.crmcustomer.entity.CrmCustomer;
import org.jeecg.modules.crmfy.crmcustomer.service.ICrmCustomerService;
import org.jeecg.modules.crmfy.crmcustomer.vo.CrmCustomerPO;
import org.jeecg.modules.crmfy.crmcustomer.vo.CrmCustomerVO;
import org.jeecg.modules.crmfy.crmproducts.entity.CrmProducts;

import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 客户信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-20
 * @Version: V1.0
 */
@Slf4j
@Api(tags="客户信息管理")
@RestController
@RequestMapping("/crmcustomer/crmCustomer")
public class CrmCustomerController extends JeecgController<CrmCustomer, ICrmCustomerService> {
	@Autowired
	private ICrmCustomerService crmCustomerService;

	/**
	 * 分页列表查询
	 *
	 * @param crmCustomer
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "客户信息管理-分页列表查询")
	@ApiOperation(value="客户信息管理-分页列表查询", notes="客户信息管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CrmCustomerVO>> queryPageList(CrmCustomerPO crmCustomer,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {



		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        if (StringUtils.isBlank(user.getOrgCode())) {
            return Result.error("该账号未绑定部门，请重试");
        }

        if(!user.getOrgCategory().equals("1")) {
        	crmCustomer.setOrgCode(user.getOrgCode());
          }
        if(user.getOrgCategory().equals("13")) {
        	crmCustomer.setUsername(user.getUsername());
        }


        Page<CrmCustomerVO> page = new Page<CrmCustomerVO>(pageNo, pageSize);

		IPage<CrmCustomerVO> pageList  = crmCustomerService.queryCustomerInfo(page,crmCustomer) ;


		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param crmCustomer
	 * @return
	 */
	@AutoLog(value = "客户信息管理-添加")
	@ApiOperation(value="客户信息管理-添加", notes="客户信息管理-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CrmCustomer crmCustomer) {
		crmCustomerService.saveCrmCustomer(crmCustomer);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param crmCustomer
	 * @return
	 */
	@AutoLog(value = "客户信息管理-编辑")
	@ApiOperation(value="客户信息管理-编辑", notes="客户信息管理-编辑")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<?> edit(@RequestBody CrmCustomer crmCustomer) {
		crmCustomerService.updateById(crmCustomer);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "客户信息管理-通过id删除")
	@ApiOperation(value="客户信息管理-通过id删除", notes="客户信息管理-通过id删除")
	@GetMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		crmCustomerService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "客户信息管理-批量删除")
	@ApiOperation(value="客户信息管理-批量删除", notes="客户信息管理-批量删除")
	@GetMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.crmCustomerService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "客户信息管理-通过id查询")
	@ApiOperation(value="客户信息管理-通过id查询", notes="客户信息管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CrmCustomerVO> queryById(@RequestParam(name="id",required=true) String id) {

		CrmCustomerPO crmCustomer= new CrmCustomerPO();
		crmCustomer.setId(Integer.parseInt(id));
		Page<CrmCustomerVO> page = new Page<CrmCustomerVO>(1, -1);

		IPage<CrmCustomerVO> pageList  = crmCustomerService.queryCustomerInfo(page,crmCustomer) ;


		List<CrmCustomerVO> list = pageList.getRecords();
		if(list.size()>0) {
			return Result.OK(list.get(0));
		}else{
			return Result.error("未找到数据");
		}
		//CrmCustomer crmCustomer = crmCustomerService.getById(id);
		//return Result.OK(crmCustomer);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param crmCustomer
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, CrmCustomerPO crmCustomer) {
    //   return super.exportXls(request, crmCustomer, CrmCustomer.class, "客户信息管理");

		LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();



        if(!user.getOrgCategory().equals("1")) {
        	crmCustomer.setOrgCode(user.getOrgCode());
          }
        if(user.getOrgCategory().equals("13")) {
        	crmCustomer.setUsername(user.getUsername());
        }


        Page<CrmCustomerVO> page = new Page<CrmCustomerVO>(1, -1);

		IPage<CrmCustomerVO> pageList  = crmCustomerService.queryCustomerInfo(page,crmCustomer) ;


		List<CrmCustomerVO> list = pageList.getRecords();
		String title = "客户信息管理";
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		// 此处设置的filename无效 ,前端会重更新设置一下
		mv.addObject(NormalExcelConstants.FILE_NAME, title);
        ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + user.getRealname(), title);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
		mv.addObject(NormalExcelConstants.CLASS, CrmCustomerVO.class);
		mv.addObject(NormalExcelConstants.DATA_LIST, list);
		return mv;
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
//  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//      return super.importExcel(request, response, CrmCustomer.class);
//  }

  /**
   * 客户模糊搜索
   *
   * @param keyword 搜索关键词
   * @return
   */
  @AutoLog(value = "客户信息管理-模糊搜索")
  @ApiOperation(value="客户信息管理-模糊搜索", notes="客户信息管理-模糊搜索")
  @GetMapping(value = "/searchCustomer")
  public Result<List<CrmCustomer>> searchCustomer(@RequestParam(name="keyword", required=false) String keyword) {
      QueryWrapper<CrmCustomer> queryWrapper = new QueryWrapper<>();

      // 如果有关键词，则按关键词搜索
      if(oConvertUtils.isNotEmpty(keyword)) {
          queryWrapper.and(wrapper -> wrapper
              .like("customer_name", keyword)
              .or().like("mobile", keyword)
              .or().like("id_number", keyword));
      }

      LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      // 根据用户角色设置查询条件
      String orgCategory = user.getOrgCategory();
      if (!"1".equals(orgCategory)) { // 非管理员
          // 销售人员只能看到自己创建的客户
          if ("13".equals(orgCategory)) {
              queryWrapper.eq("create_by", user.getUsername());
          } else {
              // 其他角色可以看到本部门的客户
              queryWrapper.eq("org_code", user.getOrgCode());
          }
      }

      // 只查询未删除的客户
      queryWrapper.eq("del_flag", 0);
      queryWrapper.orderByDesc("create_time");
      queryWrapper.last("limit 20"); // 限制返回20条记录

      List<CrmCustomer> list = crmCustomerService.list(queryWrapper);
      return Result.OK(list);
  }
}
