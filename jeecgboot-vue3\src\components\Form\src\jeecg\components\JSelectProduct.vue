<template>
  <div>
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :disabled="isDisabled"
      :mode="multiple ? 'multiple' : undefined"
      :loading="loading"
      :showSearch="true"
      :filterOption="false"
      @search="handleSearch"
      @change="handleChange"
      style="width: 100%"
    >
      <template #notFoundContent>
        <div v-if="loading">
          <a-spin size="small" />
          <span style="margin-left: 8px">搜索中...</span>
        </div>
        <div v-else-if="!supplierCode">
          请先选择供应商
        </div>
        <div v-else>
          未找到匹配的产品
        </div>
      </template>
      <a-select-option v-for="item in options" :key="item.id" :value="item.id">
        {{ item.productName || '未命名产品' }}
      </a-select-option>
    </a-select>
    <!-- <div v-if="debug" style="margin-top: 8px; font-size: 12px; color: #999; border: 1px dashed #ccc; padding: 8px; border-radius: 4px;">
      <p><strong>调试信息:</strong></p>
      <p>供应商编码: {{ supplierCode }}</p>
      <p>选中值: {{ selectedValue }}</p>
      <p>选项数量: {{ options.length }}</p>
      <div v-if="options.length > 0">
        <p><strong>选项列表:</strong></p>
        <ul style="max-height: 150px; overflow-y: auto; border: 1px solid #eee; padding: 8px; margin: 8px 0;">
          <li v-for="(item, index) in options.slice(0, 5)" :key="index">
            ID: {{ item.id }}, 名称: {{ item.productName || '未命名' }}
          </li>
          <li v-if="options.length > 5">... 还有 {{ options.length - 5 }} 个选项</li>
        </ul>
      </div>
      <p>是否禁用: {{ isDisabled }}</p>
      <p>组件禁用属性: {{ disabled }}</p>
      <p>是否有供应商: {{ !!supplierCode }}</p>
      <p style="color: red;" v-if="isDisabled">
        <strong>下拉框被禁用的原因: {{ disabled ? '组件被设置为禁用' : !supplierCode ? '未选择供应商' : '未知原因' }}</strong>
      </p>
      <div style="margin-top: 8px;">
        <a-button type="primary" size="small" @click="refreshProducts" :disabled="!supplierCode">
          手动刷新产品列表
        </a-button>
      </div>
    </div> -->
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted, PropType, computed, inject, nextTick } from 'vue';
import { searchProductsBySupplier } from '/@/api/crmfy/crmProductSelect.api';
import { debounce } from 'lodash-es';

export default defineComponent({
  name: 'JSelectProduct',
  props: {
    value: {
      type: [String, Number, Array] as PropType<string | number | Array<string | number>>,
      default: undefined,
    },
    placeholder: {
      type: String,
      default: '请输入产品名称搜索',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    supplierField: {
      type: String,
      default: 'supplierNo',
    },
    supplierNo: {
      type: String,
      default: '',
    },
    productNameField: {
      type: String,
      default: 'productName',
    },
    planNameField: {
      type: String,
      default: 'planName',
    },
    debug: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['change', 'update:value', 'update:productName', 'update:planName', 'product-selected'],
  setup(props, { emit, attrs }) {
    const loading = ref(false);
    const options = ref<any[]>([]);
    const selectedValue = ref<any>(props.value);

    // 获取表单上下文
    const formState = inject('formState', null);
    const getFormValues = inject('getFormValues', () => ({}));

    // 获取供应商编码
    const supplierCode = computed(() => {
      try {
        let code = '';

        // 首先尝试使用直接传入的supplierNo属性（优先级最高）
        if (props.supplierNo) {
          code = props.supplierNo;
          if (props.debug) console.log('使用直接传入的supplierNo属性:', code);
          return code;
        }

        // 尝试从表单状态获取
        if (formState && formState[props.supplierField]) {
          code = formState[props.supplierField];
          if (props.debug) console.log('从formState获取供应商编码:', code);
          return code;
        }

        // 尝试从getFormValues获取
        try {
          if (typeof getFormValues === 'function') {
            const formValues = getFormValues();
            if (formValues && formValues[props.supplierField]) {
              code = formValues[props.supplierField];
              if (props.debug) console.log('从getFormValues获取供应商编码:', code);
              return code;
            }
          }
        } catch (e) {
          console.error('从getFormValues获取供应商编码失败:', e);
        }

        // 尝试从attrs获取
        if (attrs && attrs[props.supplierField]) {
          code = String(attrs[props.supplierField]);
          if (props.debug) console.log('从attrs获取供应商编码:', code);
          return code;
        }

        // 尝试从model属性获取
        if (attrs && attrs.model && typeof attrs.model === 'object') {
          try {
            const model = attrs.model as any;
            if (model[props.supplierField]) {
              code = String(model[props.supplierField]);
              if (props.debug) console.log('从model属性获取供应商编码:', code);
              return code;
            }
          } catch (e) {
            console.error('从model属性获取供应商编码失败:', e);
          }
        }

        if (props.debug) {
          if (code) {
            console.log('最终获取到的供应商编码:', code);
          } else {
            console.log('未获取到供应商编码');
          }
        }

        return code;
      } catch (error) {
        console.error('获取供应商编码失败:', error);
        return '';
      }
    });

    // 监听value变化
    watch(
      () => props.value,
      (val, oldVal) => {
        try {
          console.log('value变化:', val, '旧值:', oldVal);

          // 如果值没有变化，不做处理
          if (val === oldVal) {
            return;
          }

          // 更新内部选中值
          selectedValue.value = val;

          // 如果有值且有供应商编码，且选项列表为空，则获取产品信息
          if (val && options.value.length === 0 && supplierCode.value) {
            console.log('value变化，有值且有供应商编码，获取产品信息');
            fetchProductById(val);
          } else if (!val) {
            // 如果值被清空，清空选中状态
            console.log('value被清空，清空选中状态');
            selectedValue.value = undefined;
          }
        } catch (error) {
          console.error('监听value变化处理失败:', error);
        }
      },
      { immediate: true }
    );

    // 监听供应商变化
    watch(
      () => supplierCode.value,
      (val, oldVal) => {
        try {
          console.log('供应商变化:', val, '旧值:', oldVal, '当前选中产品ID:', selectedValue.value);

          // 如果供应商编码为空，清空选项列表
          if (!val) {
            options.value = [];
            return;
          }

          // 供应商变化时，如果不是初始加载（oldVal不为空），则清空已选产品
          if (val !== oldVal && oldVal) {
            console.log('供应商变化，清空产品选择');
            selectedValue.value = undefined;
            emit('update:value', undefined);
            emit('change', undefined);

            // 清空产品名称和英文名称
            // 通过事件通知父组件更新这些字段
            emit('update:productName', '');
            emit('update:planName', '');
          }

          // 加载该供应商的产品
          nextTick(() => {
            try {
              console.log('开始加载供应商产品:', val, '当前选中产品ID:', selectedValue.value);

              // 如果有选中的产品ID，先尝试根据ID获取产品信息
              if (selectedValue.value) {
                console.log('有选中的产品ID，尝试获取产品信息:', selectedValue.value);
                fetchProductById(selectedValue.value);
              } else {
                // 否则加载所有产品
                fetchProducts(val);
              }
            } catch (error) {
              console.error('加载供应商产品失败:', error);
            }
          });
        } catch (error) {
          console.error('监听供应商变化处理失败:', error);
        }
      },
      { immediate: true }
    );

    // 根据ID获取产品信息
    async function fetchProductById(id: string | number | Array<string | number>) {
      if (!id || !supplierCode.value) {
        console.log('fetchProductById: 没有ID或供应商编码，跳过获取产品信息');
        return;
      }

      // 如果已经有选项数据，且包含当前ID，则不需要再次请求
      if (options.value.length > 0) {
        const ids = Array.isArray(id) ? id : [id];
        const allFound = ids.every(item => options.value.some(option => option.id == item));
        if (allFound) {
          console.log('fetchProductById: 已有选项数据包含当前ID，无需再次请求');
          return;
        }
      }

      loading.value = true;
      try {
        console.log('根据ID获取产品信息, 供应商编码:', supplierCode.value, 'ID:', id);
        const res = await searchProductsBySupplier(supplierCode.value, '');
        console.log('API返回结果:', res);

        // 处理API返回结果
        processApiResponse(res, 'fetchProductById');

        // 确保选中值被正确设置
        nextTick(() => {
          // 检查是否找到了对应的产品
          const ids = Array.isArray(id) ? id : [id];
          const found = ids.some(item => options.value.some(option => option.id == item));

          if (found) {
            console.log('找到了对应的产品，设置选中值:', id);
            selectedValue.value = id;

            // 找到选中的产品并触发事件
            const selectedProduct = options.value.find(item => item.id == id);
            if (selectedProduct) {
              console.log('触发产品选中事件:', selectedProduct);
              emit('product-selected', selectedProduct);
            }
          } else {
            console.warn('未找到对应的产品:', id);
          }
        });
      } catch (error) {
        console.error('获取产品信息失败:', error);
        options.value = []; // 确保在出错时设置为空数组
      } finally {
        loading.value = false;
      }
    }

    // 获取供应商下的产品
    async function fetchProducts(supplierCode: string, keyword: string = '') {
      if (!supplierCode) return;

      loading.value = true;
      try {
        if (props.debug) console.log('获取产品列表, 供应商编码:', supplierCode, '关键词:', keyword);
        const res = await searchProductsBySupplier(supplierCode, keyword);
        if (props.debug) console.log('API返回结果:', res);

        // 处理API返回结果
        processApiResponse(res, 'fetchProducts');
      } catch (error) {
        console.error('获取产品列表失败:', error);
        options.value = []; // 确保在出错时设置为空数组
      } finally {
        loading.value = false;
      }
    }

    // 处理API返回结果
    function processApiResponse(res: any, source: string) {
      try {
        // 检查返回的数据结构
        if (Array.isArray(res)) {
          console.log('返回的是数组, 长度:', res.length);
          if (res.length > 0) {
            console.log('第一个元素:', res[0]);
            console.log('id字段:', res[0].id);
            console.log('productName字段:', res[0].productName);
          }

          // 处理直接返回数组的情况
          options.value = res;
          console.log('设置选项列表:', options.value);
        } else if (res && res.success && res.result && Array.isArray(res.result)) {
          console.log('返回的是对象, result字段是数组, 长度:', res.result.length);
          if (res.result.length > 0) {
            console.log('第一个元素:', res.result[0]);
            console.log('id字段:', res.result[0].id);
            console.log('productName字段:', res.result[0].productName);
          }

          // 处理标准的Jeecg-Boot返回格式
          options.value = res.result;
          console.log('设置选项列表:', options.value);
        } else {
          console.log('返回的数据结构不是数组也不是包含result数组的对象');
          // 如果返回的不是数组，设置为空数组
          options.value = [];
          console.warn(`${source} 返回格式不正确:`, res);
        }

        // 检查设置后的options数组
        console.log('设置后的options数组长度:', options.value.length);
        if (options.value.length > 0) {
          console.log('设置后的第一个选项:', options.value[0]);
        }

        // 如果有选中值，检查是否在选项列表中
        if (selectedValue.value) {
          console.log('检查选中值是否在选项列表中:', selectedValue.value);
          const found = options.value.some(option => option.id == selectedValue.value);

          if (found) {
            console.log('选中值在选项列表中，确保选中状态正确');
            // 找到选中的产品并触发事件
            const selectedProduct = options.value.find(item => item.id == selectedValue.value);
            if (selectedProduct) {
              console.log('触发产品选中事件:', selectedProduct);
              emit('product-selected', selectedProduct);
            }
          } else {
            console.warn('选中值不在选项列表中:', selectedValue.value);
          }
        }
      } catch (error) {
        console.error(`处理API返回结果失败 (${source}):`, error);
        options.value = []; // 确保在出错时设置为空数组
      }
    }

    // 搜索产品
    const handleSearch = debounce(async (value: string) => {
      if (!supplierCode.value) return;
      fetchProducts(supplierCode.value, value);
    }, 500);

    // 选择变化
    function handleChange(value: any) {
      try {
        console.log('产品选择变化, 新值:', value);
        selectedValue.value = value;
        emit('update:value', value);
        emit('change', value);

        // 设置产品名称和英文名称
        if (value) {
          const selectedProduct = options.value.find(item => item.id == value);
          if (selectedProduct) {
            console.log('找到选中的产品信息:', selectedProduct);
            // 通过事件通知父组件更新这些字段
            emit('update:productName', selectedProduct.productName || '');
            emit('update:planName', selectedProduct.planName || '');

            // 触发自定义事件，传递选中的产品信息
            emit('product-selected', selectedProduct);
          } else {
            console.warn('未找到选中的产品信息:', value);

            // 如果没有找到产品信息，尝试重新获取产品列表
            if (supplierCode.value) {
              console.log('尝试重新获取产品信息');
              fetchProductById(value);
            }
          }
        } else {
          console.log('清空产品名称和英文名称');
          // 清空产品名称和英文名称
          emit('update:productName', '');
          emit('update:planName', '');
        }
      } catch (error) {
        console.error('处理产品选择变化失败:', error);
      }
    }

    // 组件挂载时，如果有初始值和供应商编码，则获取产品信息
    onMounted(() => {
      try {
        console.log('组件挂载, 初始值:', props.value, '供应商编码:', supplierCode.value);

        // 确保初始值被正确设置到组件的内部状态
        if (props.value) {
          console.log('设置初始选中值:', props.value);
          selectedValue.value = props.value;
        }

        // 如果有初始值和供应商编码，则获取产品信息
        if (props.value && supplierCode.value) {
          console.log('有初始值和供应商编码，获取产品信息');
          fetchProductById(props.value);
        } else if (supplierCode.value) {
          // 如果有供应商编码但没有初始值，也加载产品列表
          console.log('只有供应商编码，加载产品列表');
          fetchProducts(supplierCode.value);
        }
      } catch (error) {
        console.error('组件挂载时加载产品信息失败:', error);
      }
    });

    // 计算是否禁用
    const isDisabled = computed(() => {
      try {
        const hasSupplier = !!supplierCode.value;

        // 强制输出调试信息，帮助排查问题
        console.log('[JSelectProduct] 供应商编码:', supplierCode.value);
        console.log('[JSelectProduct] 是否有供应商编码:', hasSupplier);
        console.log('[JSelectProduct] 组件是否禁用:', props.disabled);
        console.log('[JSelectProduct] 最终禁用状态:', props.disabled || !hasSupplier);

        // 如果有供应商编码，则不禁用
        return props.disabled || !hasSupplier;
      } catch (error) {
        console.error('计算禁用状态失败:', error);
        return true; // 出错时默认禁用
      }
    });

    // 手动刷新产品列表
    function refreshProducts() {
      try {
        if (supplierCode.value) {
          if (props.debug) console.log('手动刷新产品列表, 供应商编码:', supplierCode.value);
          fetchProducts(supplierCode.value);
        }
      } catch (error) {
        console.error('刷新产品列表失败:', error);
      }
    }

    return {
      loading,
      options,
      selectedValue,
      supplierCode,
      isDisabled,
      handleSearch,
      handleChange,
      refreshProducts,
    };
  },
});
</script>
