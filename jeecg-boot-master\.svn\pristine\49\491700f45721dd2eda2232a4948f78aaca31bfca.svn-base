package org.jeecg.modules.crmfy.crmproducts.vo;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.crmfy.crmproductfiles.entity.CrmProductFiles;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CrmProductsVO implements Serializable{/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "id")
	private java.lang.Integer id;

    // @NotBlank(message = "产品编码不能为空")
    @ApiModelProperty(value = "产品编码",  example = "AIA:OYS")
    private String productCode;

    @ApiModelProperty(value = "产品名称",  example = "愛伴航")
    private String productName;

    @ApiModelProperty(value = "产品名称(英文)",  example = "Cigna Plus Medical Plan")
    private String planName;



    @ApiModelProperty(value = "是否热门产品",  example = "true")
    private Boolean hot;

    @Dict(dicCode = "product_category")
    @ApiModelProperty(value = "分类编码",  example = "1")
    private String categoryCode;

 
    @Dict(dicCode = "supplier")
    @ApiModelProperty(value = "公司代码",  example = "AIA")
    private String companyCode;

    @ApiModelProperty(value = "产品描述",  example = "「愛伴航」為您提供高達原有保額900%的保障...")
    private String desc;



    @ApiModelProperty(value = "支持币种数组",required = true, example = "[\"USD\",\"HKD\"]")
	@TableField(typeHandler = FastjsonTypeHandler.class)
    private String  currencies;

    //'缴费频率：1-月缴 2-季缴 3-半年缴 4-年缴 5-趸交',
    @ApiModelProperty(value = "缴费频率数组",  example = "[\"1\",\"2\",\"3\",\"4\",\"5\"]")
	@TableField(typeHandler = FastjsonTypeHandler.class)
    private String  frequencies;



}
