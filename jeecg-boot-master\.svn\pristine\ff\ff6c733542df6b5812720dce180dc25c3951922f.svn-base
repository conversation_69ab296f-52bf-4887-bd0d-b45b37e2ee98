package org.jeecg.modules.crmfy.crmproducts.service;

import java.util.List;

import org.jeecg.modules.crmfy.crmproducts.entity.CrmProducts;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsQueryDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsVO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductAddDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductCreateDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailVO;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 产品信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
public interface ICrmProductsService extends IService<CrmProducts> {

	void saveProducts(String productCodes);

	IPage<CrmProductsVO> queryProductInfo(Page<CrmProductsVO> page, CrmProductsQueryDTO crmProducts);

	ProductDetailVO queryDetailById(String id);

	void saveProduct(ProductAddDTO crmProduct);

	void updateCrmProductById(ProductAddDTO crmProduct);

	void removeProductByIds(List<String> asList);

}
