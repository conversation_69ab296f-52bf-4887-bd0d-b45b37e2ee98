<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="800" :showOkBtn="false">
    <template #footer>
      <div style="text-align: right;">
        <a-button @click="closeModal">取消</a-button>
        <!-- 保存草稿按钮仅在新增或销售员编辑时显示 -->
        <a-button
          v-if="showSaveDraftButton"
          type="primary"
          style="margin-left: 8px;"
          @click="handleSaveDraft"
          :loading="confirmLoading"
        >
          保存草稿
        </a-button>
        <a-button type="primary" style="margin-left: 8px;" @click="handleSubmit" :loading="confirmLoading">提交</a-button>
      </div>
    </template>
    <a-tabs>
      <a-tab-pane key="1" tab="订单信息">
        <BasicForm @register="registerForm">
          <template #productId="{ model, field }">
            <JSelectProduct
              v-model:value="model[field]"
              :supplierField="'supplierNo'"
              :supplierNo="getFieldsValue().supplierNo"
              @product-selected="handleProductSelected"
            />
          </template>
          <template #additionalInsurance>
            <div>
              <a-button type="primary" @click="handleAddAdditionalInsurance" style="margin-bottom: 8px">
                添加附加险
              </a-button>
              <a-table
                :columns="additionalColumns"
                :dataSource="additionalInsuranceList"
                :pagination="false"
                size="small"
                rowKey="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'action'">
                    <a-button type="link" @click="handleEditAdditionalInsurance(record)">编辑</a-button>
                    <a-button type="link" danger @click="handleRemoveAdditionalInsurance(record)">删除</a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </template>
          <template #policyHolderInfo>
            <a-button type="primary" @click="handleEditPolicyHolder">
              {{ policyHolderInfo ? '编辑投保人信息' : '添加投保人信息' }}
            </a-button>
            <div v-if="policyHolderInfo" style="margin-top: 8px">
              <a-descriptions :column="2" size="small" bordered>
                <a-descriptions-item label="姓名">{{ policyHolderInfo.name }}</a-descriptions-item>
                <a-descriptions-item label="证件号码">{{ policyHolderInfo.idNumber }}</a-descriptions-item>
                <a-descriptions-item label="手机号码">{{ policyHolderInfo.mobile }}</a-descriptions-item>
                <a-descriptions-item label="电子邮箱">{{ policyHolderInfo.email }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </template>
          <template #insuredInfo>
            <a-button type="primary" @click="handleEditInsured">
              {{ insuredInfo ? '编辑被保人信息' : '添加被保人信息' }}
            </a-button>
            <div v-if="insuredInfo" style="margin-top: 8px">
              <a-descriptions :column="2" size="small" bordered>
                <a-descriptions-item label="姓名">{{ insuredInfo.name }}</a-descriptions-item>
                <a-descriptions-item label="证件号码">{{ insuredInfo.idNumber }}</a-descriptions-item>
                <a-descriptions-item label="手机号码">{{ insuredInfo.mobile }}</a-descriptions-item>
                <a-descriptions-item label="电子邮箱">{{ insuredInfo.email }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </template>
          <template #beneficiaryInfo>
            <a-button type="primary" @click="handleEditBeneficiary">
              {{ beneficiaryInfo ? '编辑受益人信息' : '添加受益人信息' }}
            </a-button>
            <div v-if="beneficiaryInfo" style="margin-top: 8px">
              <a-descriptions :column="2" size="small" bordered>
                <a-descriptions-item label="姓名">{{ beneficiaryInfo.name }}</a-descriptions-item>
                <a-descriptions-item label="证件号码">{{ beneficiaryInfo.idNumber }}</a-descriptions-item>
                <a-descriptions-item label="手机号码">{{ beneficiaryInfo.mobile }}</a-descriptions-item>
                <a-descriptions-item label="电子邮箱">{{ beneficiaryInfo.email }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </template>
        </BasicForm>
      </a-tab-pane>
    </a-tabs>
  </BasicModal>

  <AdditionalInsuranceModal @register="registerAdditionalModal" @success="handleAdditionalInsuranceSuccess" />
  <PersonInfoModal @register="registerPersonInfoModal" @success="handlePersonInfoSuccess" />
</template>

<script lang="ts" setup>
  import { ref, computed, unref, watch, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import JSelectProduct from '/@/components/Form/src/jeecg/components/JSelectProduct.vue';
  import { policyFormSchema } from '../CrmPolicies.data';
  import { saveOrUpdate, getPolicyDetail, operationAudit, underwritingReply,noticeReply, feesArrival, policyIssuance, policyComplete,policyEdit } from '/@/api/crmfy/crmPolicies.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import AdditionalInsuranceModal from './AdditionalInsuranceModal.vue';
  import PersonInfoModal from './PersonInfoModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  //import { v4 as uuidv4 } from 'uuid';

  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();

  // 获取当前用户信息
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  // 判断用户角色
  const isSalesRole = computed(() => (userInfo as any)?.postCode === 'customerManager'); // 销售客户经理

  // 表单状态
  const isUpdate = ref(true);
  const record = ref<Recordable>({});
  const actionType = ref('');
  const confirmLoading = ref(false);

  // 判断是否显示保存草稿按钮 - 仅在新增或销售员编辑时显示
  const showSaveDraftButton = computed(() => {
    // 新增操作时显示
    if (!unref(isUpdate)) {
      return true;
    }

    // 销售员编辑操作时显示
    if (unref(isUpdate) && unref(isSalesRole) && !unref(actionType)) {
      return true;
    }

    return false;
  });

  // 附加险相关
  const additionalInsuranceList = ref<any[]>([]);
  const [registerAdditionalModal, { openModal: openAdditionalModal }] = useModal();

  // 附加险表格列定义
  const additionalColumns = [
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '产品名称(英文)',
      dataIndex: 'planName',
      key: 'planName',
    },
    {
      title: '每期保费',
      dataIndex: 'additionalAmount',
      key: 'additionalAmount',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
    },
  ];

  // 个人信息相关
  const policyHolderInfo = ref<Recordable | null>(null);
  const insuredInfo = ref<Recordable | null>(null);
  const beneficiaryInfo = ref<Recordable | null>(null);
  const [registerPersonInfoModal, { openModal: openPersonInfoModal }] = useModal();

  const title = computed(() => {
    if (unref(actionType)) {
      switch (unref(actionType)) {
        case 'operationAudit':
          return '审核提交';
        case 'underwritingReply':
          return '核保回复';
        case 'noticeReply':
          return '照会回复';
        case 'feesArrival':
          return '保费到账';
        case 'policyIssuance':
          return '保单签发';
        case 'policyComplete':
          return '订单完成';
        case 'policyEdit':
          return '编辑';
        default:
          return '';
      }
    }
    return !unref(isUpdate) ? '新增订单' : '编辑订单';
  });

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: policyFormSchema,
    showActionButtonGroup: false,
    // 设置基础列配置为两列布局
    baseColProps: { span: 12 },
    // 设置行属性
    rowProps: { gutter: 24 },
  });

  // 监听供应商变化，确保产品下拉框能正确响应
  watch(
    () => {
      try {
        const supplierNo = getFieldsValue().supplierNo;
        console.log('监听到供应商变化, 当前值:', supplierNo);
        return supplierNo;
      } catch (error) {
        console.error('获取供应商编码失败:', error);
        return '';
      }
    },
    (val) => {
      try {
        console.log('供应商变化处理函数被调用, 新值:', val);
        if (val) {
          // 供应商变化时，确保产品下拉框能正确响应
          nextTick(() => {
            try {
              console.log('供应商变化后的nextTick被调用');
              // 如果是编辑模式且有产品ID，不需要清空
              if (!unref(isUpdate) || !getFieldsValue().productId) {
                console.log('清空产品字段');
                setFieldsValue({
                  productId: undefined,
                  productName: '',
                  planName: '',
                });
              }

              // 强制刷新表单，确保供应商编码被正确传递
              nextTick(() => {
                console.log('刷新后的表单值:', getFieldsValue());
              });
            } catch (error) {
              console.error('设置产品字段失败:', error);
            }
          });
        }
      } catch (error) {
        console.error('监听供应商变化处理失败:', error);
      }
    }
  );

  // 处理产品选择事件
  function handleProductSelected(product: any) {
    try {
      if (product) {
        setFieldsValue({
          productName: product.productName || '',
          planName: product.planName || '',
        });

      }
    } catch (error) {
      console.error('处理产品选择事件失败:', error);
    }
  }

  // 添加附加险
  function handleAddAdditionalInsurance() {
    const supplierNo = getFieldsValue().supplierNo;
    if (!supplierNo) {
      createMessage.warning('请先选择供应商');
      return;
    }

    openAdditionalModal(true, {
      register: registerAdditionalModal,
      isUpdate: false,
      supplierNo,
    });
  }

  // 编辑附加险
  function handleEditAdditionalInsurance(record: Recordable) {
    const supplierNo = getFieldsValue().supplierNo;
    if (!supplierNo) {
      createMessage.warning('请先选择供应商');
      return;
    }

    openAdditionalModal(true, {
      register: registerAdditionalModal,
      isUpdate: true,
      record,
      supplierNo,
    });
  }

  // 删除附加险
  function handleRemoveAdditionalInsurance(record: Recordable) {
    const index = additionalInsuranceList.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      additionalInsuranceList.value.splice(index, 1);
    }
  }

  // 处理附加险表单提交
  function handleAdditionalInsuranceSuccess(formData: Recordable) {
    if (!formData.id) {
      // 新增 - 使用UUID作为临时ID
      //formData.id = uuidv4();
      additionalInsuranceList.value.push(formData);
    } else {
      // 编辑
      const index = additionalInsuranceList.value.findIndex(item => item.id === formData.id);
      if (index !== -1) {
        additionalInsuranceList.value[index] = formData;
      }
    }
  }

  // 编辑投保人信息
  function handleEditPolicyHolder() {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: !!policyHolderInfo.value,
      record: policyHolderInfo.value || {},
      personType: 'policyHolder',
    });
  }

  // 编辑被保人信息
  function handleEditInsured() {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: !!insuredInfo.value,
      record: insuredInfo.value || {},
      personType: 'insured',
    });
  }

  // 编辑受益人信息
  function handleEditBeneficiary() {
    openPersonInfoModal(true, {
      register: registerPersonInfoModal,
      isUpdate: !!beneficiaryInfo.value,
      record: beneficiaryInfo.value || {},
      personType: 'beneficiary',
    });
  }

  // 处理个人信息表单提交
  function handlePersonInfoSuccess(result: { type: string; data: Recordable }) {
    const { type, data } = result;

    switch (type) {
      case 'policyHolder':
        policyHolderInfo.value = data;
        break;
      case 'insured':
        insuredInfo.value = data;
        break;
      case 'beneficiary':
        beneficiaryInfo.value = data;
        break;
    }
  }

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    try {
      resetFields();
      setModalProps({ confirmLoading: false });
      isUpdate.value = !!data?.isUpdate;
      actionType.value = data?.actionType || '';

      // 清空附加险列表和个人信息
      additionalInsuranceList.value = [];
      policyHolderInfo.value = null;
      insuredInfo.value = null;
      beneficiaryInfo.value = null;

      if (unref(isUpdate)) {
        // 保存原始记录，以便后续使用ID等信息
        record.value = { ...data.record };

        // 编辑模式下，调用API获取完整的订单详情
        console.log('正在获取订单详情，ID:', data.record.id);
        setModalProps({ confirmLoading: true });

        try {
          // 调用API获取详细数据
          const detailData = await getPolicyDetail(data.record.id);
          console.log('获取到的订单详情数据:', detailData);

          // 记录产品ID，以便后续处理
          const productId = detailData.productId;
          console.log('订单详情中的产品ID:', productId);

          // 使用获取到的详情数据填充表单
          await setFieldsValue({
            ...detailData,
          });

          // 确保产品ID被正确设置
          if (productId) {
            console.log('重新设置产品ID:', productId);
            // 延迟设置产品ID，确保供应商已经被正确加载
            setTimeout(() => {
              setFieldsValue({
                productId: productId
              });
              console.log('产品ID设置完成，当前表单值:', getFieldsValue());
            }, 500);
          }

          // 处理附加险数据
          if (detailData.crmPolicyAdditionals && Array.isArray(detailData.crmPolicyAdditionals)) {
            additionalInsuranceList.value = [...detailData.crmPolicyAdditionals];
          }

          // 处理投保人信息
          if (detailData.crmPolicyHolders) {
            policyHolderInfo.value = detailData.crmPolicyHolders;
          }

          // 处理被保人信息
          if (detailData.crmPolicyInsureds) {
            insuredInfo.value = detailData.crmPolicyInsureds;
          }

          // 处理受益人信息
          if (detailData.crmPolicyBeneficiaries) {
            beneficiaryInfo.value = detailData.crmPolicyBeneficiaries;
          }

        } catch (detailError) {
          console.error('获取订单详情失败:', detailError);
          createMessage.error('获取订单详情失败，请重试');

          // 如果获取详情失败，回退到使用传入的数据
          await setFieldsValue({
            ...data.record,
          });

          // 如果有附加险数据，加载到列表中
          if (data.record.crmPolicyAdditionals && Array.isArray(data.record.crmPolicyAdditionals)) {
            additionalInsuranceList.value = [...data.record.crmPolicyAdditionals];
          }

          // 如果有个人信息数据，加载到对应字段
          if (data.record.crmPolicyHolders) {
            policyHolderInfo.value = data.record.crmPolicyHolders;
          }

          if (data.record.crmPolicyInsureds) {
            insuredInfo.value = data.record.crmPolicyInsureds;
          }

          if (data.record.crmPolicyBeneficiaries) {
            beneficiaryInfo.value = data.record.crmPolicyBeneficiaries;
          }
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }
    } catch (error) {
      console.error('初始化表单失败:', error);
      setModalProps({ confirmLoading: false });
    }
  });

  // 保存草稿
  async function handleSaveDraft() {
    try {
      await submitForm('0');
    } catch (error: any) {
      console.error('保存草稿失败:', error);
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      await submitForm('1');
    } catch (error: any) {
      console.error('提交表单失败:', error);
    }
  }

  // 通用表单提交处理
  async function submitForm(izSubmit: string) {
    try {
      const values = await validate();
      confirmLoading.value = true;
      setModalProps({ confirmLoading: true });

      // 处理表单数据
      const formData = {
        ...values,
        izSubmit, // 设置是否提交标志
        auditResult:'1', // 审核标志非必填
        crmPolicyAdditionals: additionalInsuranceList.value,
        crmPolicyHolders: policyHolderInfo.value,
        crmPolicyInsureds: insuredInfo.value,
        crmPolicyBeneficiaries: beneficiaryInfo.value,
      };

      // 如果是更新操作，需要传入ID
      if (unref(isUpdate)) {
        formData.id = record.value.id;
      }

      // 根据操作类型调用不同的API
      //运营岗审核提交
      if (unref(actionType) === 'operationAudit') {
        await operationAudit(formData);
      } else if (unref(actionType) === 'underwritingReply') {
        await underwritingReply(formData);
      } else if(unref(actionType) === 'feesArrival') {
        await feesArrival(formData);
      } else if(unref(actionType) === 'policyIssuance') {
        await policyIssuance(formData);
      } else if(unref(actionType) === 'policyComplete') {
        await policyComplete(formData);
      }else if(unref(actionType) === 'noticeReply'){
        await noticeReply(formData);
      }else if(unref(actionType) === 'policyEdit'){
        await policyEdit(formData);
      }else {
        // 销售员保存/提交
        await saveOrUpdate(formData, unref(isUpdate));
      }

      closeModal();
      emit('success');
      createMessage.success(izSubmit === '1' ? `提交成功` : `保存草稿成功`);
    } catch (error: any) {
      console.error('操作失败:', error);
      createMessage.error(`操作失败: ${error?.message || '未知错误'}`);
    } finally {
      confirmLoading.value = false;
      setModalProps({ confirmLoading: false });
    }
  }
</script>
