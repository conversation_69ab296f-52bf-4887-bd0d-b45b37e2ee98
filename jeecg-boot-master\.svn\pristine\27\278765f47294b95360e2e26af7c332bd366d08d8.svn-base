package org.jeecg.modules.crmfy.crmproducts.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.crmfy.crmdetailattributes.entity.CrmDetailAttributes;
import org.jeecg.modules.crmfy.crmdetailattributes.service.ICrmDetailAttributesService;
import org.jeecg.modules.crmfy.crmproductcontractrate.entity.CrmProductContractRate;
import org.jeecg.modules.crmfy.crmproductcontractrate.mapper.CrmProductContractRateMapper;
import org.jeecg.modules.crmfy.crmproductfiles.entity.CrmProductFiles;
import org.jeecg.modules.crmfy.crmproductfiles.service.ICrmProductFilesService;
import org.jeecg.modules.crmfy.crmproductpaymentterms.entity.CrmProductPaymentTerms;
import org.jeecg.modules.crmfy.crmproductpaymentterms.mapper.CrmProductPaymentTermsMapper;
import org.jeecg.modules.crmfy.crmproducts.entity.CrmProducts;
import org.jeecg.modules.crmfy.crmproducts.mapper.CrmProductsMapper;
import org.jeecg.modules.crmfy.crmproducts.service.ICrmProductsService;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsQueryDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.CrmProductsVO;
import org.jeecg.modules.crmfy.crmproducts.vo.PaymentTermDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductAddDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductCreateDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailDTO;
import org.jeecg.modules.crmfy.crmproducts.vo.ProductDetailVO;
import org.jeecg.modules.crmfy.crmproductsdetails.entity.CrmProductsDetails;
import org.jeecg.modules.crmfy.crmproductsdetails.service.ICrmProductsDetailsService;
import org.jeecg.modules.jsjx.utils.DataUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 产品信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-16
 * @Version: V1.0
 */
@Slf4j
@Service
public class CrmProductsServiceImpl extends ServiceImpl<CrmProductsMapper, CrmProducts> implements ICrmProductsService {
	
	@Autowired
	private ICrmProductsDetailsService crmProductsDetailsService;
	
	@Autowired
	private ICrmDetailAttributesService  crmDetailAttributesService;
	
	@Autowired
	private ICrmProductFilesService crmProductFilesService;


	@Autowired
	private CrmProductPaymentTermsMapper crmProductPaymentTermsMapper;
	@Autowired
	private CrmProductContractRateMapper crmProductContractRateMapper;
		
	@Transactional
	@Override
	public void saveProducts(String productCodes) {
		
        LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();

		String[] productCodesArray = productCodes.split(",");
		if(productCodesArray.length>0) {
			
			//https://mp.lifebee.tech/product/detail?instance=yufung&accountName=OSYZHI&productCode=AIA%3AOYS&shareCode=iBV2PgCh&language=zh-Hant-HK
			for(String productCode:productCodesArray) {
				String shareCode =generateRandomString();
				String url =  "https://api.lifebee.tech/web/v3/product/detail/"+productCode ;//"https://mp.lifebee.tech/product/detail?instance=yufung&accountName=OSYZHI&productCode="+productCode+"&shareCode="+shareCode+"&language=zh-Hant-HK";
//				String result= HttpUtil.get(url, CharsetUtil.CHARSET_UTF_8);
				Map<String, String> headers = new HashMap<>();
		        // 头部传参

		        headers.put("Referer", "https://mp.lifebee.tech/");
		        headers.put("X-Timestamp", System.currentTimeMillis()+"");
		        headers.put("X-BeeFintech-OS", "WEB");
		        headers.put("X-BeeFintech-Instance", "yufung");
		        headers.put("X-BeeFintech-Language", "zh-Hant-HK");
		        headers.put("language", "zh-Hant-HK");
		        headers.put("Origin", "https://mp.lifebee.tech");
		        String result ="";
		         result = HttpUtil.createGet(url)
							.addHeaders(headers)
							.contentType("application/json")
							.timeout(1000)
							.execute().body();      //execute 执行

				 JSONObject obj = JSON.parseObject(result).getJSONObject("data");
		         
		         ProductCreateDTO dto = JSON.parseObject(obj.toJSONString(), ProductCreateDTO.class);
		         
		         log.info(JSON.toJSONString(dto));
		         
		         CrmProducts proc = new CrmProducts();
		         
				 BeanUtils.copyProperties(dto,proc);
		         this.save(proc);

				 List<ProductDetailDTO> details = dto.getDetails();
				 
				 List<CrmProductFiles> files =  dto.getFiles() ;
				 
				 List<CrmDetailAttributes> attrs = new ArrayList<>();
		         for(ProductDetailDTO pdto :details) {
		        	 CrmProductsDetails detail = new CrmProductsDetails();
		        	 BeanUtils.copyProperties(pdto,detail );
		        	 detail.setProductCode(dto.getProductCode());
//		        	 detailList.add(detail);
		        	 crmProductsDetailsService.save(detail);
		        	 
		        	 List<CrmDetailAttributes>  attrList = pdto.getAttributes();
		        	 for(CrmDetailAttributes attr :attrList) {
		        		 attr.setDetailId(detail.getId());
		        		 attrs.add(attr) ;
		        	 }
		         }
		         crmDetailAttributesService.saveBatch(attrs);
		         
		         
		         for(CrmProductFiles file :files) {
		        	 file.setProductCode(dto.getProductCode());

		         }
		         crmProductFilesService.saveBatch(files);
			}
			
			
		}
		
		
	}
	
	
	public static String generateRandomString() {
        // 创建Random对象
        Random random = new Random();
        
        // 定义字符集
        String lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz";
        String upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String letters = lowerCaseLetters + upperCaseLetters;
        String digits = "0123456789";
        
        // 创建StringBuilder来构建结果
        StringBuilder result = new StringBuilder();
        
        // 先生成7个随机字母
        for (int i = 0; i < 7; i++) {
            int index = random.nextInt(letters.length());
            result.append(letters.charAt(index));
        }
        
        // 生成1个随机数字
        char randomDigit = digits.charAt(random.nextInt(digits.length()));
        
        // 随机选择位置插入数字
        int digitPosition = random.nextInt(8); // 0-7的位置
        result.insert(digitPosition, randomDigit);
        
        return result.toString();
    }


	@Override
	public IPage<CrmProductsVO> queryProductInfo(Page<CrmProductsVO> page, CrmProductsQueryDTO crmProductsQueryDTO) {
		return this.baseMapper.queryProductInfo(page, crmProductsQueryDTO);
	}


	@Override
	public ProductDetailVO queryDetailById(String id) {
		return this.baseMapper.queryDetailById(id);
	}


	@Transactional
	@Override
	public void saveProduct(ProductAddDTO dto) {
        
        log.info(JSON.toJSONString(dto));

		//判断是否存在
		CrmProducts proc1 = this.getOne(new LambdaQueryWrapper<CrmProducts>().eq(CrmProducts::getPlanName, dto.getPlanName()).eq(CrmProducts::getProductStatus, "1").eq(CrmProducts::getCompanyCode, dto.getCompanyCode()));
		if(proc1!=null) {
			throw new JeecgBootException("产品名称(英文)已存在");
		}
        CrmProducts proc = new CrmProducts();
        
		 BeanUtils.copyProperties(dto,proc);
		 if(StringUtils.isBlank(dto.getProductCode())) {
			 String productCode = dto.getCompanyCode()+"::"+DataUtils.generateAcronym(dto.getPlanName().replace("Plan", "")) ;
			 proc.setProductCode(productCode);
		 }

		 proc.setProductStatus("1");
		 proc.setDelFlag(0);
		 List<PaymentTermDTO> paymentTerms = dto.getPaymentTerms();
		 List<String> list = new ArrayList<>();
		 for(PaymentTermDTO pt :paymentTerms) {
			 list.add(pt.getPaymentTerm().toString());
		 }
		 proc.setPaymentTerm((JSONArray) JSONObject.toJSON(list));
		 this.save(proc);

		 //保存paymentTerms
		 for(PaymentTermDTO pt :paymentTerms) {
			 CrmProductPaymentTerms c = new CrmProductPaymentTerms();
			 c.setPaymentTerm(pt.getPaymentTerm());
			 c.setProductId(proc.getId());
			 c.setDelFlag(0);
	
			 crmProductPaymentTermsMapper.insert(c);
			 
			 for(CrmProductContractRate cr :pt.getContractRates()) {
				 cr.setProductId(proc.getId());
				 cr.setTermId(c.getId());
				 cr.setPaymentTerm(pt.getPaymentTerm());
				 cr.setDelFlag(0);
				 crmProductContractRateMapper.insert(cr);
			 }
		 }
		 
       

		//  List<ProductDetailDTO> details = dto.getDetails();
		 
		 List<CrmProductFiles> files =  dto.getFiles() ;
		 
//		 List<CrmDetailAttributes> attrs = new ArrayList<>();
//        for(ProductDetailDTO pdto :details) {
//       	 CrmProductsDetails detail = new CrmProductsDetails();
//       	 BeanUtils.copyProperties(pdto,detail );
//       	 detail.setProductCode(dto.getProductCode());
//       	 crmProductsDetailsService.save(detail);
//       	 
//       	 List<CrmDetailAttributes>  attrList = pdto.getAttributes();
//       	 for(CrmDetailAttributes attr :attrList) {
//       		 attr.setDetailId(detail.getId());
//       		 attrs.add(attr) ;
//       	 }
//        }
//        crmDetailAttributesService.saveBatch(attrs);
        
        
        for(CrmProductFiles file :files) {
       	 file.setProductCode(dto.getProductCode());
       	 file.setProductId(proc.getId());
       	 file.setDelFlag(0);
        }
        crmProductFilesService.saveBatch(files);
		
	}


	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateCrmProductById(ProductAddDTO dto) {
		CrmProducts proc = new CrmProducts();
        
		 BeanUtils.copyProperties(dto,proc);

		 if(StringUtils.isBlank(dto.getProductCode())) {
			String productCode = dto.getCompanyCode()+"::"+DataUtils.generateAcronym(dto.getPlanName().replace("Plan", "")) ;
			proc.setProductCode(productCode);
		 }

		 List<PaymentTermDTO> paymentTerms = dto.getPaymentTerms();
		 List<String> list = new ArrayList<>();
		 for(PaymentTermDTO pt :paymentTerms) {
			 list.add(pt.getPaymentTerm().toString());
		 }
		 proc.setPaymentTerm((JSONArray) JSONObject.toJSON(list));

         this.updateById(proc);

		 //保存paymentTerms

		 crmProductPaymentTermsMapper.delete(new LambdaQueryWrapper<CrmProductPaymentTerms>().eq(CrmProductPaymentTerms::getProductId, proc.getId()));
		 crmProductContractRateMapper.delete(new LambdaQueryWrapper<CrmProductContractRate>().eq(CrmProductContractRate::getProductId, proc.getId()));

		 for(PaymentTermDTO pt :paymentTerms) {		 
			 CrmProductPaymentTerms c = new CrmProductPaymentTerms();
			 c.setPaymentTerm(pt.getPaymentTerm());
			 c.setProductId(proc.getId());
			 c.setDelFlag(0);
			 crmProductPaymentTermsMapper.insert(c);
			 
			 for(CrmProductContractRate cr :pt.getContractRates()) {
				 cr.setProductId(proc.getId());
				 cr.setTermId(c.getId());
				 cr.setPaymentTerm(pt.getPaymentTerm());
				 cr.setDelFlag(0);
				 crmProductContractRateMapper.insert(cr);
			 }
		 }

		//  List<ProductDetailDTO> details = dto.getDetails();
		 
		 List<CrmProductFiles> files =  dto.getFiles() ;
		 
//		 List<CrmProductsDetails> detailList  = new ArrayList<>();
//		 
//		 List<CrmDetailAttributes> attrs = new ArrayList<>();
//       for(ProductDetailDTO pdto :details) {
//      	 CrmProductsDetails detail = new CrmProductsDetails();
//      	 BeanUtils.copyProperties(pdto,detail );
//      	 detail.setProductCode(dto.getProductCode());
//      	 detailList.add(detail);
//      	 
//      	 
//      	 List<CrmDetailAttributes>  attrList = pdto.getAttributes();
//      	 for(CrmDetailAttributes attr :attrList) {
//      		 attr.setDetailId(detail.getId());
//      		 attrs.add(attr) ;
//      	 }
//       }
//       crmDetailAttributesService.updateBatchById(attrs);
//       crmProductsDetailsService.updateBatchById(detailList);

		//删除原有文件
		 crmProductFilesService.remove(new LambdaQueryWrapper<CrmProductFiles>().eq(CrmProductFiles::getProductId, proc.getId()));
		 
		 //新增文件
		 for(CrmProductFiles file :files) {
	       	 file.setProductCode(dto.getProductCode());
	       	 file.setProductId(proc.getId());
	       	 file.setDelFlag(0);
	        }
	        crmProductFilesService.saveBatch(files);
       
		
	}


	@Transactional
	@Override
	public void removeProductByIds(List<String> ids) {
		this.removeBatchByIds(ids);
		for(String id :ids) {
			crmProductPaymentTermsMapper.delete(new LambdaQueryWrapper<CrmProductPaymentTerms>().eq(CrmProductPaymentTerms::getProductId, id));
			crmProductContractRateMapper.delete(new LambdaQueryWrapper<CrmProductContractRate>().eq(CrmProductContractRate::getProductId, id));
			crmProductFilesService.remove(new LambdaQueryWrapper<CrmProductFiles>().eq(CrmProductFiles::getProductId, id));

			// CrmProducts proc = this.getById(id);
			// String productCode = proc.getProductCode() ;
			
			//  LambdaQueryWrapper<CrmProductsDetails> queryWrapper = new LambdaQueryWrapper<>();
		    //  queryWrapper.eq(CrmProductsDetails::getProductCode, productCode);
		    //  crmProductsDetailsService.remove(queryWrapper);
		     
		    //  crmDetailAttributesService.remove(new LambdaQueryWrapper<CrmDetailAttributes>().eq(CrmDetailAttributes::getProductCode, productCode));
		     
		    //  crmProductFilesService.remove(new LambdaQueryWrapper<CrmProductFiles>().eq(CrmProductFiles::getProductCode, productCode));

			
		}
		
	}

}
