package org.jeecg.modules.jsjx.sysuserlog.controller;

import java.text.ParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.jsjx.sysuserlog.entity.SysUserLogCus;
import org.jeecg.modules.jsjx.sysuserlog.service.ISysUserLogCusService;
import org.jeecg.modules.jsjx.sysuserlog.vo.SysUserLogCusExcel;
import org.jeecg.modules.jsjx.vo.SysUserLogCusChangePO;
import org.jeecg.modules.jsjx.vo.SysUserLogCusPO;
import org.jeecg.modules.jsjx.sysuserlog.vo.SysUserLogCancelPO;
import org.jeecg.modules.jsjx.utils.BeanCopyUtil;
import org.jeecg.modules.jsjx.utils.MyDateUtils;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

 /**
 * @Description: sys_user_log
 * @Author: jeecg-boot
 * @Date:   2022-09-06
 * @Version: V1.0
 */
// @Api(tags="人事异动管理")
@RestController
@RequestMapping("/customize/sysUserCusLog")
@Slf4j
public class SysUserLogCusController extends JeecgController<SysUserLogCus, ISysUserLogCusService> {
	@Autowired
	private ISysUserLogCusService sysUserLogService;
	
	@Autowired
	private RedisUtil redisUtil;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysUserLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "sys_user_log-分页列表查询")
	// @ApiOperation(value="sys_user_log-分页列表查询", notes="sys_user_log-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysUserLogCus>> queryPageList(SysUserLogCus sysUserLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SysUserLogCus> queryWrapper = QueryGenerator.initQueryWrapper(sysUserLog, req.getParameterMap());
		Page<SysUserLogCus> page = new Page<SysUserLogCus>(pageNo, pageSize);
		IPage<SysUserLogCus> pageList = sysUserLogService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	//@AutoLog(value = "sys_user_log-分页列表查询")
	@SuppressWarnings("unchecked")
	// @ApiOperation(value="人事异动-分页列表查询", notes="人事异动-分页列表查询")
	@GetMapping(value = "/empChangeList")
	public Result<IPage<SysUserLogCus>> empChangeList(SysUserLogCusPO sysUserLog,
                                                      @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                      @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                      HttpServletRequest req) {
		
		 LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();
	     
	     if (StringUtils.isBlank( user.getOrgCode())) {
	         return Result.error("该账号未绑定部门，请重试");
	     }
//	     Map<String, String> map = new HashMap<>();
//	     map = (Map<String, String>) redisUtil.hget("orgInf:", user.getOrgCode()) ;
//	    String orgCategory =  map.get("orgCategory") ;
//	    if(orgCategory.equals("11")||orgCategory.equals("1")) {//分公司/市公司  查询所有员工状态是 1 的员工
//	    	sysUserLog.setOpeStatus("0");
//	    }else
	    	
//	   if(orgCategory.equals("13")) {//油站 查询 本站员工状态是1 和 调站状态是待确认的 1
//	    	sysUserLog.setOpeStatus("1");
//	    }
	    if(StringUtils.isEmpty(sysUserLog.getOrgCode())) {
	    	sysUserLog.setOrgCode(user.getOrgCode());
	    }
	    
		Page<SysUserLogCus> page = new Page<SysUserLogCus>(pageNo, pageSize);
		
		
		IPage<SysUserLogCus> pageList  = sysUserLogService.querySysUserLog(page,sysUserLog) ;
		
		return Result.OK(pageList);
	}
	
	@SuppressWarnings("unchecked")
	@AutoLog(value = "人事异动-调站发起")
	// @ApiOperation(value="调站发起", notes="调站发起")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:edit")
	@RequestMapping(value = "/changeNodeApply", method = {RequestMethod.POST})
	public Result<String> changeNodeApply(@RequestBody SysUserLogCusChangePO sysUserLog) {
		LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		
	     Map<String, String> map = new HashMap<>();
	     map = (Map<String, String>) redisUtil.hget("orgInf:", user.getOrgCode()) ;
	    String orgCategory =  map.get("orgCategory") ;
	    if(orgCategory.equals("13")) {//油站不能发起调站
	    	return Result.error("油站不能操作调站发起!");
	    }
	   try {
		if(MyDateUtils.stringToDate( sysUserLog.getInDate()).compareTo( MyDateUtils.stringToDate( sysUserLog.getOutDate()))<0) {
			return Result.error("调出日期不能大于调入日期!");
		}
		   			
	} catch (ParseException e) {
		
		e.printStackTrace();
		return Result.error("调入/调出日期格式错误!");
	}
	   
	   sysUserLogService.changeNodeApply(sysUserLog,user);
		return Result.OK("调站发起成功!");
	}
	

	
	// @ApiOperation(value="员工服务号校验重复", notes="员工服务号校验重复")
	@GetMapping(value = "/checkWorkNo")
	public Result<String> checkWorkNo(String workNo,String orgCode ,HttpServletRequest req) {
//		String tenantId = TenantContext.getTenant() ;
		QueryWrapper<SysUserLogCus> queryWrapper = new QueryWrapper<SysUserLogCus>();
		queryWrapper.eq("work_no", workNo).eq("del_flag", "0").eq("emp_status","1").eq("org_code", orgCode);

		SysUserLogCus sysUser =sysUserLogService.getOne(queryWrapper) ;
		if(sysUser==null) {
			return Result.OK("不重复");
		}
		return Result.error("员工服务号重复！");
		
	}
	
	
	@AutoLog(value = "人事异动-调站撤销")
	// @ApiOperation(value="人事异动-调站撤销", notes="人事异动-调站撤销")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:edit")
	@RequestMapping(value = "/changeNodeCancel", method = {RequestMethod.POST})
	public Result<String> changeNodeCheck(@RequestBody SysUserLogCancelPO sysUserLog) {
		LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		sysUserLogService.changeNodeCancel(sysUserLog,user);
		return Result.OK("调站撤销成功!");
	}	
	
	
	
	@SuppressWarnings("unchecked")
	@AutoLog(value = "人事异动-调站确认")
	// @ApiOperation(value="调站确认", notes="调站确认")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:edit")
	@RequestMapping(value = "/changeNodeActive", method = {RequestMethod.POST})
	public Result<String> changeNodeActive(@RequestBody SysUserLogCusChangePO sysUserLog) {
		LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		
		  Map<String, String> map = new HashMap<>();
		     map = (Map<String, String>) redisUtil.hget("orgInf:", user.getOrgCode()) ;
		    String orgCategory =  map.get("orgCategory") ;
		    if(!orgCategory.equals("13")) {//非油站不能调站确认
		    	return Result.error("非油站用户不能操作调站确认!");
		    }
		sysUserLogService.changeNodeActive(sysUserLog,user);
		return Result.OK("调站确认成功!");
	}	
	
	@AutoLog(value = "人事异动-离职")
	// @ApiOperation(value="离职", notes="离职")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:edit")
	@RequestMapping(value = "/empLeave", method = {RequestMethod.POST})
	public Result<String> empLeave(@RequestBody SysUserLogCusChangePO sysUserLog) {
		LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		sysUserLogService.empLeave(sysUserLog,user);
		return Result.OK("离职操作成功!");
	}	
	
	
	
	

//	@ApiOperation(value="sys_user_log-分页列表查询2", notes="sys_user_log-分页列表查询2")
//	@GetMapping(value = "/list2")
//	public Result<List<SysUserLogCusVO>> queryPageList2(SysUserLogCus sysUserLog,
//								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//								   HttpServletRequest req) {
////		QueryWrapper<SysUserLogCus> queryWrapper = QueryGenerator.initQueryWrapper(sysUserLog, req.getParameterMap());
////		Page<SysUserLogCusVO> page = new Page<SysUserLogCusVO>(pageNo, pageSize);
//		List<SysUserLogCusVO> pageList = sysUserLogService.list2() ;
//		return Result.OK(pageList);
//	}
	
	/**
	 *   添加
	 *
	 * @param sysUserLog
	 * @return
	 */
	@AutoLog(value = "sys_user_log-添加")
//	@ApiOperation(value="sys_user_log-添加", notes="sys_user_log-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysUserLogCus sysUserLog) {
		sysUserLogService.save(sysUserLog);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysUserLog
	 * @return
	 */
	@AutoLog(value = "sys_user_log-编辑")
//	@ApiOperation(value="sys_user_log-编辑", notes="sys_user_log-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:edit")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<String> edit(@RequestBody SysUserLogCus sysUserLog) {
		sysUserLogService.updateById(sysUserLog);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "sys_user_log-通过id删除")
//	@ApiOperation(value="sys_user_log-通过id删除", notes="sys_user_log-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:delete")
	@GetMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysUserLogService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "sys_user_log-批量删除")
//	@ApiOperation(value="sys_user_log-批量删除", notes="sys_user_log-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:emp_inf:deleteBatch")
	@GetMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysUserLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "sys_user_log-通过id查询")
	// @ApiOperation(value="-通过id查询", notes="通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysUserLogCus> queryById(@RequestParam(name="id",required=true) String id) {
		SysUserLogCus sysUserLog = sysUserLogService.getById(id);
		if(sysUserLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysUserLog);
	}

    /**
    * 导出excel
    *
    * @param
    * @param sysUserLog
    */
//	@RequiresPermissions("staffset:export")
	@AutoLog(value = "人事异动-导出")
// @ApiOperation(value = "人事异动-查询导出", notes = "人事异动-查询导出")
@GetMapping(value = "/exportExcel")
public ModelAndView exportExcel(SysUserLogCusPO sysUserLog, HttpServletRequest req) {
		 LoginUser user = (LoginUser)SecurityUtils.getSubject().getPrincipal();
	     
	     if (StringUtils.isBlank( user.getOrgCode())) {
	         throw new RuntimeException("该账号未绑定部门，请重试");
	     }
//	     Map<String, String> map = new HashMap<>();
//	     map = (Map<String, String>) redisUtil.hget("orgInf:", user.getOrgCode()) ;
//	    String orgCategory =  map.get("orgCategory") ;
//	    if(orgCategory.equals("11")||orgCategory.equals("1")) {//分公司/市公司  查询所有员工状态是 1 的员工
//	    	sysUserLog.setOpeStatus("0");
//	    }else
	    	
//	   if(orgCategory.equals("13")) {//油站 查询 本站员工状态是1 和 调站状态是待确认的 1
//	    	sysUserLog.setOpeStatus("1");
//	    }
	    if(StringUtils.isEmpty(sysUserLog.getOrgCode())) {
	    	sysUserLog.setOrgCode(user.getOrgCode());
	    }
	    
		Page<SysUserLogCus> page = new Page<SysUserLogCus>(1, -1);
		
		
		IPage<SysUserLogCus> pageList  = sysUserLogService.querySysUserLog(page,sysUserLog) ;
	        List<SysUserLogCus> list = pageList.getRecords();
	        
	        Object result = parseDictText(list);
	        List<SysUserLogCusExcel> exportList = JSON.parseArray(result.toString(), SysUserLogCusExcel.class);
	        
	        
//	        List<SysUserLogCusExcel> exportList = new ArrayList<>();
//            Map<String, String> map = new HashMap<>();


         exportList =  BeanCopyUtil.copyListProperties(exportList, SysUserLogCusExcel::new, (po, vo) ->{
    // 这里可以定义特定的转换规则
	        	@SuppressWarnings("unchecked")
			    Map<String, String>  map = (Map<String, String>)redisUtil.hget("orgInf:", po.getOrgCode());	 
//	            vo.setCompanyNo(map.get("parentSinopecNodeno"));
	            vo.setCompanyName(map.get("parentName"));
//	            ruleNodeAchieveFactorExcelVO.setOrgCode(map.get("sinopecNodeno"));
	            vo.setDepartName(map.get("departName"));
});

  String title = "人事异动";
  ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
  // 此处设置的filename无效 ,前端会重更新设置一下
  mv.addObject(NormalExcelConstants.FILE_NAME, title);
  mv.addObject(NormalExcelConstants.CLASS, SysUserLogCusExcel.class);
  ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + user.getRealname(), title);
  mv.addObject(NormalExcelConstants.PARAMS, exportParams);
  mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
  return mv;
}  

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    //@RequiresPermissions("emp_inf:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysUserLogCus.class);
    }

}
