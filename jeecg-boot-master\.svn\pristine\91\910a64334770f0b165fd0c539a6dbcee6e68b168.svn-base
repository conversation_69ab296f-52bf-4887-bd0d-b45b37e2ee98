package org.jeecg.modules.crmfy.crmproducts.vo;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.crmfy.crmproductfiles.entity.CrmProductFiles;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductDetailVO implements Serializable{/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(value = "id")
	private java.lang.Integer id;

    @ApiModelProperty(value = "产品唯一编码", required = false, example = "AIA:OYS")
    private String productCode;

    @NotBlank(message = "产品名称不能为空")
    @ApiModelProperty(value = "产品名称", required = true, example = "愛伴航")
    private String productName;

    @NotBlank(message = "产品名称(英文)不能为空")
    @ApiModelProperty(value = "产品名称(英文)", required = true, example = "Cigna Plus Medical Plan")
    private String planName;



    @NotNull(message = "是否热门必须指定")
    @ApiModelProperty(value = "是否热门产品", required = true, example = "true")
    private Boolean hot;



    @Dict(dicCode = "product_category")
    @NotBlank(message = "分类编码不能为空")
    @ApiModelProperty(value = "分类编码", required = true, example = "1")
    private String categoryCode;


    @Dict( dicCode = "supplier")
    @ApiModelProperty(value = "公司代码", required = true, example = "AIA")
    private String companyCode;



    @NotBlank(message = "产品描述不能为空")
    @ApiModelProperty(value = "产品描述", required = true, example = "「愛伴航」為您提供高達原有保額900%的保障...")
    private String desc;



    @NotEmpty(message = "必须指定至少一种货币")
    @ApiModelProperty(value = "支持币种数组",required = true, example = "[\"USD\",\"HKD\"]")
	@TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray currencies;

    //'缴费频率：1-月缴 2-季缴 3-半年缴 4-年缴 5-趸交',
    @NotEmpty(message = "必须指定至少一种缴费频率")
    @ApiModelProperty(value = "缴费频率数组", required = true, example = "[\"1\",\"2\",\"3\",\"4\",\"5\"]")
	@TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONArray frequencies;



//    // 缴费年期及对应合约手续费率列表 
    @ApiModelProperty(value = "缴费年期及对应合约手续费率列表", required = true)
    List<PaymentTermDTO> paymentTerms;


    @ApiModelProperty(value = "产品文件信息", required = false)
    private List<CrmProductFiles> files ;

    @Dict(dicCode = "product_status")
    @ApiModelProperty(value = "产品状态 1-有效 0-下架")
	private java.lang.String productStatus;
}

