package org.jeecg.modules.crmfy.crmpolicies.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 订单信息管理
 * @Author: jeecg-boot
 * @Date:   2025-03-28
 * @Version: V1.0
 */
@Data
@TableName("crm_policies")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="crm_policies对象", description="订单信息管理")
public class CrmPolicies {
    
	/**保单ID*/
	@TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "保单ID")
	private java.lang.Integer id;
	/**供应商名称*/
	@Excel(name = "供应商", width = 15)
    @ApiModelProperty(value = "供应商")
	private java.lang.String supplierNo;
	/**保险产品名称*/
	@Excel(name = "保险产品名称", width = 15)
    @ApiModelProperty(value = "保险产品名称")
	private java.lang.String productName;
//	/**保险产品编码*/
//	@Excel(name = "保险产品编码", width = 15)
//    @ApiModelProperty(value = "保险产品编码")
//	private java.lang.String productCode;
	
	@Excel(name = "保险产品名称(英文)", width = 15)
    @ApiModelProperty(value = "保险产品名称(英文)")
	private java.lang.String planName;
	
    @ApiModelProperty(value = "保险产品id")
	private java.lang.Integer productId;
	/**缴费年期*/
	@Excel(name = "缴费年期", width = 15)
    @ApiModelProperty(value = "缴费年期")
	private java.lang.Integer paymentTerm;
	/**缴费频率*/
	@Excel(name = "缴费频率", width = 15)
    @ApiModelProperty(value = "缴费频率")
	private java.lang.String paymentFrequency;
	/**缴费币种*/
	@Excel(name = "缴费币种", width = 15)
    @ApiModelProperty(value = "缴费币种")
	private java.lang.String paymentCurrency;
	/**缴费方式*/
	@Excel(name = "缴费方式", width = 15)
    @ApiModelProperty(value = "缴费方式")
	private java.lang.String paymentMethod;
	/**缴费金额*/
	@Excel(name = "缴费金额", width = 15)
    @ApiModelProperty(value = "缴费金额")
	private java.math.BigDecimal paymentAmount;
	/**保单号码*/
	@Excel(name = "保单号码", width = 15)
    @ApiModelProperty(value = "保单号码")
	private java.lang.String policyNo;
	/**保障期限*/
	@Excel(name = "保障期限", width = 15)
    @ApiModelProperty(value = "保障期限")
	private java.lang.String coveragePeriod;
	/**是否自动扣款*/
	@Excel(name = "是否自动扣款", width = 15)
    @ApiModelProperty(value = "是否自动扣款")
	private java.lang.Boolean autoDebit;
	/**优惠方案*/
	@Excel(name = "优惠方案", width = 15)
    @ApiModelProperty(value = "优惠方案")
	private java.lang.String promotionPlan;
	/**通知电邮地址*/
	@Excel(name = "通知电邮地址", width = 15)
    @ApiModelProperty(value = "通知电邮地址")
	private java.lang.String notificationEmail;
	/**电子签收链接*/
	@Excel(name = "电子签收链接", width = 15)
    @ApiModelProperty(value = "电子签收链接")
	private java.lang.String signatureSms;
	/**当前数据版本*/
	@Excel(name = "当前数据版本", width = 15)
    @ApiModelProperty(value = "当前数据版本")
	private java.lang.Integer currentVersion;
	/**保单状态*/
	@Excel(name = "保单状态", width = 15)
    @ApiModelProperty(value = "保单状态 0草稿、1已签单待提交、2已提交待核保、3照会待处理、4照会已回复待核保、5核保通过待保费到账、6保费到账待签发保单、7保单签发、8过冷静期(生效)、9订单完成、-1退保")
	private java.lang.String policyStatus;

	// @Excel(name = "部门", width = 15)
    @ApiModelProperty(value = "部门")
	private java.lang.String orgCode;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private java.lang.String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private java.util.Date createTime;
	/**修改人*/
	@Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
	private java.lang.String updateBy;
	/**修改时间*/
	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
	private java.util.Date updateTime;
	/**0表示未删除,1表示删除*/
	@Excel(name = "0表示未删除,1表示删除", width = 15)
    @ApiModelProperty(value = "0表示未删除,1表示删除")
	private java.lang.Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @ApiModelProperty(value = "租户ID")
	private java.lang.Integer tenantId;
	/**缴费日当天汇率*/
	@Excel(name = "缴费日当天汇率", width = 15)
    @ApiModelProperty(value = "缴费日当天汇率")
	private java.math.BigDecimal fypExchangeRate;
	/**佣金结算日当天汇率*/
	@Excel(name = "佣金结算日当天汇率", width = 15)
    @ApiModelProperty(value = "佣金结算日当天汇率")
	private java.math.BigDecimal rcExchangeRate;


	@ApiModelProperty(value = "客户ID")
	private java.lang.Integer customerId;

	@ApiModelProperty(value = "销售经理账号")
	private java.lang.String salesUsername;

	@ApiModelProperty(value = "保单唯一标识")
	private java.lang.String policyUuid;

	/**照会说明*/
	@Excel(name = "照会说明", width = 15)
    @ApiModelProperty(value = "照会说明")
	private java.lang.String noticeRemark;

	@ApiModelProperty(value = "照会回复-")
	private java.lang.String noticeReply;

	@ApiModelProperty(value = "是否有效：0-历史版本 1-当前版本")
	private java.lang.String izActive;

	@ApiModelProperty(value = "下一个节点流程 状态：1-保单提交 2-运营审核 3-核保回复 4-照会回复 5-保费到账确认 6-保单签发 7-保单完成")
	private java.lang.String nextNode;

	@ApiModelProperty(value = "保单签发日期")
	private Date policyIssueDate;

	@ApiModelProperty(value = "冷静期结束日期")
	private Date coolingOffDate;

	@ApiModelProperty(value = "返佣状态 1 返佣中 0返佣结束")
	private String commissionStatus;
	

	// `policy_issue_date` date DEFAULT NULL COMMENT '保单签发日期',
	// `cooling_off_date` date DEFAULT NULL COMMENT '冷静期结束日期',

	
	
}
